{"level":"fatal","timestamp":"2025-07-24T17:51:17.970Z","context":[],"errors":[{"name":"<PERSON>rror","message":"Unhandled rejection:","stack":"Error: Unhandled rejection:\n    at process.<anonymous> (file:///Users/<USER>/PJ/new-farmer/node_modules/.pnpm/@kdt310722+logger@0.0.12_@kdt310722+utils@0.0.19/node_modules/@kdt310722/logger/dist/index.js:731:13)\n    at process.emit (node:events:518:28)\n    at #processEmit (file:///Users/<USER>/PJ/new-farmer/node_modules/.pnpm/signal-exit@4.1.0/node_modules/signal-exit/dist/mjs/index.js:241:23)\n    at #process.emit (file:///Users/<USER>/PJ/new-farmer/node_modules/.pnpm/signal-exit@4.1.0/node_modules/signal-exit/dist/mjs/index.js:187:37)\n    at emitUnhandledRejection (node:internal/process/promises:252:13)\n    at throwUnhandledRejectionsMode (node:internal/process/promises:388:19)\n    at processPromiseRejections (node:internal/process/promises:475:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:106:32)"}],"metadata":{}}
{"level":"fatal","timestamp":"2025-07-25T09:51:50.416Z","context":[],"errors":[{"name":"Error","message":"Unhandled rejection:","stack":"Error: Unhandled rejection:\n    at process.<anonymous> (file:///Users/<USER>/PJ/new-farmer/node_modules/.pnpm/@kdt310722+logger@0.0.12_@kdt310722+utils@0.0.19/node_modules/@kdt310722/logger/dist/index.js:731:13)\n    at process.emit (node:events:518:28)\n    at #processEmit (file:///Users/<USER>/PJ/new-farmer/node_modules/.pnpm/signal-exit@4.1.0/node_modules/signal-exit/dist/mjs/index.js:241:23)\n    at #process.emit (file:///Users/<USER>/PJ/new-farmer/node_modules/.pnpm/signal-exit@4.1.0/node_modules/signal-exit/dist/mjs/index.js:187:37)\n    at emitUnhandledRejection (node:internal/process/promises:252:13)\n    at throwUnhandledRejectionsMode (node:internal/process/promises:388:19)\n    at processPromiseRejections (node:internal/process/promises:475:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:106:32)"}],"metadata":{}}
