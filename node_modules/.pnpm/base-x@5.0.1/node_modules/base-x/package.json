{"name": "base-x", "version": "5.0.1", "description": "Fast base encoding / decoding of any given alphabet", "type": "module", "keywords": ["base-x", "base58", "base62", "base64", "crypto", "crytography", "decode", "decoding", "encode", "encoding"], "homepage": "https://github.com/cryptocoinjs/base-x", "bugs": {"url": "https://github.com/cryptocoinjs/base-x/issues"}, "license": "MIT", "author": "<PERSON>", "files": ["src"], "main": "src/cjs/index.cjs", "module": "src/esm/index.js", "types": "src/cjs/index.d.ts", "exports": {".": {"require": "./src/cjs/index.cjs", "import": "./src/esm/index.js", "types": "./src/cjs/index.d.ts"}}, "repository": {"type": "git", "url": "https://github.com/cryptocoinjs/base-x.git"}, "scripts": {"build": "npm run clean && tsc -p ./tsconfig.json && tsc -p ./tsconfig.cjs.json; npm run standard -- --fix", "clean": "rimraf src", "postbuild": "find src/cjs -type f -name \"*.js\" -exec bash -c 'mv \"$0\" \"${0%.js}.cjs\"' {} \\;", "gitdiff": "npm run build && git diff --exit-code", "prepublish": "npm run gitdiff", "standard": "standard --ignore test", "test": "npm run unit && npm run standard", "unit": "tape test/*.js"}, "devDependencies": {"@types/node": "12.0.10", "rimraf": "^3.0.2", "standard": "^17.1.0", "tape": "^5.3.0", "typescript": "^5.4.5"}}