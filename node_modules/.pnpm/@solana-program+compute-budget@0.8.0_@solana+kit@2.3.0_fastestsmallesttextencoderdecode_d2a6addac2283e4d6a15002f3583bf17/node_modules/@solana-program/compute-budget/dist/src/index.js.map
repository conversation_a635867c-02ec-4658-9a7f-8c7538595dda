{"version": 3, "sources": ["../../src/generated/programs/computeBudget.ts", "../../src/generated/instructions/requestHeapFrame.ts", "../../src/generated/instructions/requestUnits.ts", "../../src/generated/instructions/setComputeUnitLimit.ts", "../../src/generated/instructions/setComputeUnitPrice.ts", "../../src/generated/instructions/setLoadedAccountsDataSizeLimit.ts", "../../src/constants.ts", "../../src/internal.ts", "../../src/setComputeLimit.ts", "../../src/estimateAndSetComputeLimit.ts", "../../src/internalMoveToKit.ts", "../../src/estimateComputeLimitInternal.ts", "../../src/estimateComputeLimit.ts", "../../src/setComputePrice.ts"], "names": ["ComputeBudgetInstruction", "containsBytes", "getU8Encoder", "transformEncoder", "getStructEncoder", "getU32Encoder", "getStructDecoder", "getU8Decoder", "getU32Decoder", "combineCodec", "getU64Encoder", "getU64Decoder", "previousUnits", "appendTransactionMessageInstruction", "estimateComputeUnitLimit", "setTransactionMessageLifetimeUsingBlockhash", "isDurableNonceTransaction", "pipe", "compileTransaction", "getBase64EncodedWireTransaction", "SolanaError", "SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT", "SOLANA_ERROR__TRANSACTION__FAILED_WHEN_SIMULATING_TO_ESTIMATE_COMPUTE_LIMIT", "isSolana<PERSON>rror", "previousMicroLamports"], "mappings": ";;;;;AAsBO,IAAM,8BACX,GAAA,8CAAA;AAEU,IAAA,wBAAA,qBAAAA,yBAAL,KAAA;AACL,EAAAA,yBAAA,CAAA,yBAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAA,CAAA;AACA,EAAAA,yBAAA,CAAA,yBAAA,CAAA,kBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,kBAAA,CAAA;AACA,EAAAA,yBAAA,CAAA,yBAAA,CAAA,qBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,qBAAA,CAAA;AACA,EAAAA,yBAAA,CAAA,yBAAA,CAAA,qBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,qBAAA,CAAA;AACA,EAAAA,yBAAA,CAAA,yBAAA,CAAA,gCAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gCAAA,CAAA;AALU,EAAAA,OAAAA,yBAAAA,CAAAA;AAAA,CAAA,EAAA,wBAAA,IAAA,EAAA,EAAA;AAQL,SAAS,iCACd,WAC0B,EAAA;AAC1B,EAAA,MAAM,IAAO,GAAA,MAAA,IAAU,WAAc,GAAA,WAAA,CAAY,IAAO,GAAA,WAAA,CAAA;AACxD,EAAI,IAAAC,iBAAA,CAAc,MAAMC,gBAAa,EAAA,CAAE,OAAO,CAAC,CAAA,EAAG,CAAC,CAAG,EAAA;AACpD,IAAO,OAAA,CAAA,oBAAA;AAAA,GACT;AACA,EAAI,IAAAD,iBAAA,CAAc,MAAMC,gBAAa,EAAA,CAAE,OAAO,CAAC,CAAA,EAAG,CAAC,CAAG,EAAA;AACpD,IAAO,OAAA,CAAA,wBAAA;AAAA,GACT;AACA,EAAI,IAAAD,iBAAA,CAAc,MAAMC,gBAAa,EAAA,CAAE,OAAO,CAAC,CAAA,EAAG,CAAC,CAAG,EAAA;AACpD,IAAO,OAAA,CAAA,2BAAA;AAAA,GACT;AACA,EAAI,IAAAD,iBAAA,CAAc,MAAMC,gBAAa,EAAA,CAAE,OAAO,CAAC,CAAA,EAAG,CAAC,CAAG,EAAA;AACpD,IAAO,OAAA,CAAA,2BAAA;AAAA,GACT;AACA,EAAI,IAAAD,iBAAA,CAAc,MAAMC,gBAAa,EAAA,CAAE,OAAO,CAAC,CAAA,EAAG,CAAC,CAAG,EAAA;AACpD,IAAO,OAAA,CAAA,sCAAA;AAAA,GACT;AACA,EAAA,MAAM,IAAI,KAAA;AAAA,IACR,kFAAA;AAAA,GACF,CAAA;AACF,CAAA;;;AC3BO,IAAM,gCAAmC,GAAA,EAAA;AAEzC,SAAS,qCAAwC,GAAA;AACtD,EAAOA,OAAAA,gBAAAA,EAAe,CAAA,MAAA,CAAO,gCAAgC,CAAA,CAAA;AAC/D,CAAA;AA0BO,SAAS,yCAA0F,GAAA;AACxG,EAAO,OAAAC,oBAAA;AAAA,IACLC,oBAAiB,CAAA;AAAA,MACf,CAAC,eAAiBF,EAAAA,gBAAAA,EAAc,CAAA;AAAA,MAChC,CAAC,OAAS,EAAAG,iBAAA,EAAe,CAAA;AAAA,KAC1B,CAAA;AAAA,IACD,CAAC,KAAW,MAAA,EAAE,GAAG,KAAA,EAAO,eAAe,gCAAiC,EAAA,CAAA;AAAA,GAC1E,CAAA;AACF,CAAA;AAEO,SAAS,yCAAsF,GAAA;AACpG,EAAA,OAAOC,oBAAiB,CAAA;AAAA,IACtB,CAAC,eAAiB,EAAAC,gBAAA,EAAc,CAAA;AAAA,IAChC,CAAC,OAAS,EAAAC,iBAAA,EAAe,CAAA;AAAA,GAC1B,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,uCAGd,GAAA;AACA,EAAO,OAAAC,gBAAA;AAAA,IACL,yCAA0C,EAAA;AAAA,IAC1C,yCAA0C,EAAA;AAAA,GAC5C,CAAA;AACF,CAAA;AAMO,SAAS,8BAAA,CAGd,OACA,MAC8C,EAAA;AAE9C,EAAM,MAAA,cAAA,GACJ,QAAQ,cAAkB,IAAA,8BAAA,CAAA;AAG5B,EAAM,MAAA,IAAA,GAAO,EAAE,GAAG,KAAM,EAAA,CAAA;AAExB,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,cAAA;AAAA,IACA,IAAA,EAAM,2CAA4C,CAAA,MAAA;AAAA,MAChD,IAAA;AAAA,KACF;AAAA,GACF,CAAA;AAEA,EAAO,OAAA,WAAA,CAAA;AACT,CAAA;AASO,SAAS,iCACd,WAC6C,EAAA;AAC7C,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,IAAM,EAAA,yCAAA,EAA4C,CAAA,MAAA,CAAO,YAAY,IAAI,CAAA;AAAA,GAC3E,CAAA;AACF,CAAA;AClGO,IAAM,2BAA8B,GAAA,EAAA;AAEpC,SAAS,iCAAoC,GAAA;AAClD,EAAOP,OAAAA,gBAAAA,EAAe,CAAA,MAAA,CAAO,2BAA2B,CAAA,CAAA;AAC1D,CAAA;AAwBO,SAAS,qCAAkF,GAAA;AAChG,EAAOC,OAAAA,oBAAAA;AAAA,IACLC,oBAAiB,CAAA;AAAA,MACf,CAAC,eAAiBF,EAAAA,gBAAAA,EAAc,CAAA;AAAA,MAChC,CAAC,OAASG,EAAAA,iBAAAA,EAAe,CAAA;AAAA,MACzB,CAAC,eAAiBA,EAAAA,iBAAAA,EAAe,CAAA;AAAA,KAClC,CAAA;AAAA,IACD,CAAC,KAAW,MAAA,EAAE,GAAG,KAAA,EAAO,eAAe,2BAA4B,EAAA,CAAA;AAAA,GACrE,CAAA;AACF,CAAA;AAEO,SAAS,qCAA8E,GAAA;AAC5F,EAAA,OAAOC,oBAAiB,CAAA;AAAA,IACtB,CAAC,eAAiBC,EAAAA,gBAAAA,EAAc,CAAA;AAAA,IAChC,CAAC,OAASC,EAAAA,iBAAAA,EAAe,CAAA;AAAA,IACzB,CAAC,eAAiBA,EAAAA,iBAAAA,EAAe,CAAA;AAAA,GAClC,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,mCAGd,GAAA;AACA,EAAOC,OAAAA,gBAAAA;AAAA,IACL,qCAAsC,EAAA;AAAA,IACtC,qCAAsC,EAAA;AAAA,GACxC,CAAA;AACF,CAAA;AAOO,SAAS,0BAAA,CAGd,OACA,MAC0C,EAAA;AAE1C,EAAM,MAAA,cAAA,GACJ,QAAQ,cAAkB,IAAA,8BAAA,CAAA;AAG5B,EAAM,MAAA,IAAA,GAAO,EAAE,GAAG,KAAM,EAAA,CAAA;AAExB,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,cAAA;AAAA,IACA,IAAA,EAAM,uCAAwC,CAAA,MAAA;AAAA,MAC5C,IAAA;AAAA,KACF;AAAA,GACF,CAAA;AAEA,EAAO,OAAA,WAAA,CAAA;AACT,CAAA;AASO,SAAS,6BACd,WACyC,EAAA;AACzC,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,IAAM,EAAA,qCAAA,EAAwC,CAAA,MAAA,CAAO,YAAY,IAAI,CAAA;AAAA,GACvE,CAAA;AACF,CAAA;ACnGO,IAAM,oCAAuC,GAAA,EAAA;AAE7C,SAAS,wCAA2C,GAAA;AACzD,EAAOP,OAAAA,gBAAAA,EAAe,CAAA,MAAA,CAAO,oCAAoC,CAAA,CAAA;AACnE,CAAA;AAoBO,SAAS,4CAAgG,GAAA;AAC9G,EAAOC,OAAAA,oBAAAA;AAAA,IACLC,oBAAiB,CAAA;AAAA,MACf,CAAC,eAAiBF,EAAAA,gBAAAA,EAAc,CAAA;AAAA,MAChC,CAAC,OAASG,EAAAA,iBAAAA,EAAe,CAAA;AAAA,KAC1B,CAAA;AAAA,IACD,CAAC,KAAW,MAAA;AAAA,MACV,GAAG,KAAA;AAAA,MACH,aAAe,EAAA,oCAAA;AAAA,KACjB,CAAA;AAAA,GACF,CAAA;AACF,CAAA;AAEO,SAAS,4CAA4F,GAAA;AAC1G,EAAA,OAAOC,oBAAiB,CAAA;AAAA,IACtB,CAAC,eAAiBC,EAAAA,gBAAAA,EAAc,CAAA;AAAA,IAChC,CAAC,OAASC,EAAAA,iBAAAA,EAAe,CAAA;AAAA,GAC1B,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,0CAGd,GAAA;AACA,EAAOC,OAAAA,gBAAAA;AAAA,IACL,4CAA6C,EAAA;AAAA,IAC7C,4CAA6C,EAAA;AAAA,GAC/C,CAAA;AACF,CAAA;AAMO,SAAS,iCAAA,CAGd,OACA,MACiD,EAAA;AAEjD,EAAM,MAAA,cAAA,GACJ,QAAQ,cAAkB,IAAA,8BAAA,CAAA;AAG5B,EAAM,MAAA,IAAA,GAAO,EAAE,GAAG,KAAM,EAAA,CAAA;AAExB,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,cAAA;AAAA,IACA,IAAA,EAAM,8CAA+C,CAAA,MAAA;AAAA,MACnD,IAAA;AAAA,KACF;AAAA,GACF,CAAA;AAEA,EAAO,OAAA,WAAA,CAAA;AACT,CAAA;AASO,SAAS,oCACd,WACgD,EAAA;AAChD,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,IAAA,EAAM,8CAA+C,CAAA,MAAA;AAAA,MACnD,WAAY,CAAA,IAAA;AAAA,KACd;AAAA,GACF,CAAA;AACF,CAAA;ACjGO,IAAM,oCAAuC,GAAA,EAAA;AAE7C,SAAS,wCAA2C,GAAA;AACzD,EAAOP,OAAAA,gBAAAA,EAAe,CAAA,MAAA,CAAO,oCAAoC,CAAA,CAAA;AACnE,CAAA;AAoBO,SAAS,4CAAgG,GAAA;AAC9G,EAAOC,OAAAA,oBAAAA;AAAA,IACLC,oBAAiB,CAAA;AAAA,MACf,CAAC,eAAiBF,EAAAA,gBAAAA,EAAc,CAAA;AAAA,MAChC,CAAC,eAAiB,EAAAQ,iBAAA,EAAe,CAAA;AAAA,KAClC,CAAA;AAAA,IACD,CAAC,KAAW,MAAA;AAAA,MACV,GAAG,KAAA;AAAA,MACH,aAAe,EAAA,oCAAA;AAAA,KACjB,CAAA;AAAA,GACF,CAAA;AACF,CAAA;AAEO,SAAS,4CAA4F,GAAA;AAC1G,EAAA,OAAOJ,oBAAiB,CAAA;AAAA,IACtB,CAAC,eAAiBC,EAAAA,gBAAAA,EAAc,CAAA;AAAA,IAChC,CAAC,eAAiB,EAAAI,iBAAA,EAAe,CAAA;AAAA,GAClC,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,0CAGd,GAAA;AACA,EAAOF,OAAAA,gBAAAA;AAAA,IACL,4CAA6C,EAAA;AAAA,IAC7C,4CAA6C,EAAA;AAAA,GAC/C,CAAA;AACF,CAAA;AAMO,SAAS,iCAAA,CAGd,OACA,MACiD,EAAA;AAEjD,EAAM,MAAA,cAAA,GACJ,QAAQ,cAAkB,IAAA,8BAAA,CAAA;AAG5B,EAAM,MAAA,IAAA,GAAO,EAAE,GAAG,KAAM,EAAA,CAAA;AAExB,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,cAAA;AAAA,IACA,IAAA,EAAM,8CAA+C,CAAA,MAAA;AAAA,MACnD,IAAA;AAAA,KACF;AAAA,GACF,CAAA;AAEA,EAAO,OAAA,WAAA,CAAA;AACT,CAAA;AASO,SAAS,oCACd,WACgD,EAAA;AAChD,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,IAAA,EAAM,8CAA+C,CAAA,MAAA;AAAA,MACnD,WAAY,CAAA,IAAA;AAAA,KACd;AAAA,GACF,CAAA;AACF,CAAA;ACjGO,IAAM,iDAAoD,GAAA,EAAA;AAE1D,SAAS,mDAAsD,GAAA;AACpE,EAAA,OAAOP,kBAAe,CAAA,MAAA;AAAA,IACpB,iDAAA;AAAA,GACF,CAAA;AACF,CAAA;AAkBO,SAAS,uDAAsH,GAAA;AACpI,EAAOC,OAAAA,oBAAAA;AAAA,IACLC,oBAAiB,CAAA;AAAA,MACf,CAAC,eAAiBF,EAAAA,gBAAAA,EAAc,CAAA;AAAA,MAChC,CAAC,sBAAwBG,EAAAA,iBAAAA,EAAe,CAAA;AAAA,KACzC,CAAA;AAAA,IACD,CAAC,KAAW,MAAA;AAAA,MACV,GAAG,KAAA;AAAA,MACH,aAAe,EAAA,iDAAA;AAAA,KACjB,CAAA;AAAA,GACF,CAAA;AACF,CAAA;AAEO,SAAS,uDAAkH,GAAA;AAChI,EAAA,OAAOC,oBAAiB,CAAA;AAAA,IACtB,CAAC,eAAiBC,EAAAA,gBAAAA,EAAc,CAAA;AAAA,IAChC,CAAC,sBAAwBC,EAAAA,iBAAAA,EAAe,CAAA;AAAA,GACzC,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,qDAGd,GAAA;AACA,EAAOC,OAAAA,gBAAAA;AAAA,IACL,uDAAwD,EAAA;AAAA,IACxD,uDAAwD,EAAA;AAAA,GAC1D,CAAA;AACF,CAAA;AAMO,SAAS,4CAAA,CAGd,OACA,MAC4D,EAAA;AAE5D,EAAM,MAAA,cAAA,GACJ,QAAQ,cAAkB,IAAA,8BAAA,CAAA;AAG5B,EAAM,MAAA,IAAA,GAAO,EAAE,GAAG,KAAM,EAAA,CAAA;AAExB,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,cAAA;AAAA,IACA,IAAA,EAAM,yDAA0D,CAAA,MAAA;AAAA,MAC9D,IAAA;AAAA,KACF;AAAA,GACF,CAAA;AAEA,EAAO,OAAA,WAAA,CAAA;AACT,CAAA;AASO,SAAS,+CAGd,WAC2D,EAAA;AAC3D,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,IAAA,EAAM,yDAA0D,CAAA,MAAA;AAAA,MAC9D,WAAY,CAAA,IAAA;AAAA,KACd;AAAA,GACF,CAAA;AACF,CAAA;;;ACzHO,IAAM,4BAA+B,GAAA,EAAA;AAKrC,IAAM,sBAAyB,GAAA,KAAA;ACS/B,SAAS,+CACd,kBACyC,EAAA;AACzC,EAAM,MAAA,KAAA,GAAQ,uCAAuC,kBAAkB,CAAA,CAAA;AACvE,EAAA,IAAI,QAAQ,CAAG,EAAA;AACb,IAAO,OAAA,IAAA,CAAA;AAAA,GACT;AAEA,EAAM,MAAA,KAAA,GAAQD,mBAAgB,CAAA,MAAA;AAAA,IAC5B,kBAAA,CAAmB,YAAa,CAAA,KAAK,CAAE,CAAA,IAAA;AAAA,IACvC,CAAA;AAAA,GACF,CAAA;AAEA,EAAO,OAAA,EAAE,OAAO,KAAM,EAAA,CAAA;AACxB,CAAA;AAKO,SAAS,uCACd,kBACA,EAAA;AACA,EAAA,OAAO,mBAAmB,YAAa,CAAA,SAAA;AAAA,IACrC,gCAAA;AAAA,GACF,CAAA;AACF,CAAA;AAKO,SAAS,iCACd,WAC+C,EAAA;AAC/C,EAAA,OACE,WAAY,CAAA,cAAA,KAAmB,8BAC/B,IAAA,gCAAA,CAAiC,YAAY,IAAkB,CAAA,KAAA,CAAA,2BAAA;AAGnE,CAAA;AAMO,SAAS,uDACd,kBACwD,EAAA;AACxD,EAAM,MAAA,KAAA,GAAQ,uCAAuC,kBAAkB,CAAA,CAAA;AACvE,EAAA,IAAI,QAAQ,CAAG,EAAA;AACb,IAAO,OAAA,IAAA,CAAA;AAAA,GACT;AAEA,EAAM,MAAA,aAAA,GAAgBG,mBAAgB,CAAA,MAAA;AAAA,IACpC,kBAAA,CAAmB,YAAa,CAAA,KAAK,CAAE,CAAA,IAAA;AAAA,IACvC,CAAA;AAAA,GACF,CAAA;AAEA,EAAO,OAAA,EAAE,OAAO,aAAc,EAAA,CAAA;AAChC,CAAA;AAKO,SAAS,uCACd,kBACA,EAAA;AACA,EAAA,OAAO,mBAAmB,YAAa,CAAA,SAAA;AAAA,IACrC,gCAAA;AAAA,GACF,CAAA;AACF,CAAA;AAKO,SAAS,iCACd,WAC+C,EAAA;AAC/C,EAAA,OACE,WAAY,CAAA,cAAA,KAAmB,8BAC/B,IAAA,gCAAA,CAAiC,YAAY,IAAkB,CAAA,KAAA,CAAA,2BAAA;AAGnE,CAAA;AChFO,SAAS,4CAEd,kBAAyC,EAAA;AACzC,EAAO,OAAA,4CAAA;AAAA,IACL,CAAC,aAAA,KACC,aAAkB,KAAA,IAAA,GAAO,4BAA+B,GAAA,aAAA;AAAA,IAC1D,kBAAA;AAAA,GACF,CAAA;AACF,CAAA;AAoBO,SAAS,4CAAA,CAGd,OACA,kBACqB,EAAA;AACrB,EAAM,MAAA,QAAA,GAAW,CAACC,cAChB,KAAA,OAAO,UAAU,UAAa,GAAA,KAAA,CAAMA,cAAa,CAAI,GAAA,KAAA,CAAA;AACvD,EAAM,MAAA,kBAAA,GACJ,+CAA+C,kBAAkB,CAAA,CAAA;AAEnE,EAAA,IAAI,CAAC,kBAAoB,EAAA;AACvB,IAAO,OAAAC,uCAAA;AAAA,MACL,kCAAkC,EAAE,KAAA,EAAO,QAAS,CAAA,IAAI,GAAG,CAAA;AAAA,MAC3D,kBAAA;AAAA,KACF,CAAA;AAAA,GACF;AAEA,EAAA,MAAM,EAAE,KAAA,EAAO,KAAO,EAAA,aAAA,EAAkB,GAAA,kBAAA,CAAA;AACxC,EAAM,MAAA,QAAA,GAAW,SAAS,aAAa,CAAA,CAAA;AACvC,EAAA,IAAI,aAAa,aAAe,EAAA;AAC9B,IAAO,OAAA,kBAAA,CAAA;AAAA,GACT;AAEA,EAAA,MAAM,cAAiB,GAAA,iCAAA,CAAkC,EAAE,KAAA,EAAO,UAAU,CAAA,CAAA;AAC5E,EAAA,MAAM,eAAkB,GAAA,CAAC,GAAG,kBAAA,CAAmB,YAAY,CAAA,CAAA;AAC3D,EAAgB,eAAA,CAAA,MAAA,CAAO,KAAO,EAAA,CAAA,EAAG,cAAc,CAAA,CAAA;AAC/C,EAAA,OAAO,OAAO,MAAO,CAAA;AAAA,IACnB,GAAG,kBAAA;AAAA,IACH,YAAc,EAAA,eAAA;AAAA,GACf,CAAA,CAAA;AACH,CAAA;;;ACtCO,SAAS,kDACdC,yBAC2D,EAAA;AAC3D,EAAO,OAAA,eAAe,EAAG,CAAA,kBAAA,EAAoB,MAAQ,EAAA;AACnD,IAAM,MAAA,kBAAA,GACJ,+CAA+C,kBAAkB,CAAA,CAAA;AAKnE,IAAA,IACE,sBACA,kBAAmB,CAAA,KAAA,KAAU,4BAC7B,IAAA,kBAAA,CAAmB,UAAU,sBAC7B,EAAA;AACA,MAAO,OAAA,kBAAA,CAAA;AAAA,KACT;AAEA,IAAO,OAAA,4CAAA;AAAA,MACL,MAAMA,yBAAyB,CAAA,kBAAA,EAAoB,MAAM,CAAA;AAAA,MACzD,kBAAA;AAAA,KACF,CAAA;AAAA,GACF,CAAA;AACF,CAAA;AChDO,IAAM,uCACX,GAAA;AAAA,EACE,SAAW,EAAA,kCAAA;AAAA,EACX,oBAAsB,EAAA,EAAA;AAAA;AACxB,CAAA,CAAA;AAMK,SAAS,6DAGd,kBAKE,EAAA;AAOF,EAAA,IAAI,wBAAwB,kBAAoB,EAAA;AAC9C,IAAO,OAAA,kBAAA,CAAA;AAAA,GACT;AAEA,EAAO,OAAA,oDAAA;AAAA,IACL,kBAAA;AAAA,GACF,CAAA;AACF,CAAA;AAKO,SAAS,qDAGd,kBAC+D,EAAA;AAC/D,EAAO,OAAAC,+CAAA;AAAA,IACL,uCAAA;AAAA,IACA,kBAAA;AAAA,GACF,CAAA;AACF,CAAA;;;ACsDA,eAAsB,wBAAyB,CAAA;AAAA,EAC7C,kBAAA;AAAA,EACA,GAAG,OAAA;AACL,CAAoD,EAAA;AAClD,EAAM,MAAA,sBAAA,GAAyB,CAACC,6BAAA,CAA0B,kBAAkB,CAAA,CAAA;AAC5E,EAAA,MAAM,WAAc,GAAAC,QAAA;AAAA,IAClB,kBAAA;AAAA,IACA,4DAAA;AAAA,IACA,CAAC,CAAA,KACC,4CAA6C,CAAA,sBAAA,EAAwB,CAAC,CAAA;AAAA,IACxEC,sBAAA;AAAA,GACF,CAAA;AAEA,EAAA,OAAO,MAAM,sCAAuC,CAAA;AAAA,IAClD,WAAA;AAAA,IACA,sBAAA;AAAA,IACA,GAAG,OAAA;AAAA,GACJ,CAAA,CAAA;AACH,CAAA;AAQA,eAAe,sCAAuC,CAAA;AAAA,EACpD,WAAA;AAAA,EACA,GAAA;AAAA,EACA,WAAA;AAAA,EACA,GAAG,cAAA;AACL,CAAkE,EAAA;AAChE,EAAM,MAAA,oBAAA,GAAuBC,oCAAgC,WAAW,CAAA,CAAA;AAExE,EAAI,IAAA;AACF,IAAM,MAAA;AAAA,MACJ,KAAO,EAAA,EAAE,GAAK,EAAA,gBAAA,EAAkB,aAAc,EAAA;AAAA,KAC5C,GAAA,MAAM,GACP,CAAA,mBAAA,CAAoB,oBAAsB,EAAA;AAAA,MACzC,GAAG,cAAA;AAAA,MACH,QAAU,EAAA,QAAA;AAAA,MACV,SAAW,EAAA,KAAA;AAAA,KACZ,CAAA,CACA,IAAK,CAAA,EAAE,aAAa,CAAA,CAAA;AACvB,IAAA,IAAI,iBAAiB,IAAM,EAAA;AAEzB,MAAA,MAAM,IAAIC,eAAA;AAAA,QACRC,+DAAA;AAAA,OACF,CAAA;AAAA,KACF;AAIA,IAAA,MAAM,qBACJ,GAAA,aAAA,GAAgB,WAAiB,GAAA,UAAA,GAAgB,OAAO,aAAa,CAAA,CAAA;AACvE,IAAA,IAAI,gBAAkB,EAAA;AACpB,MAAA,MAAM,IAAID,eAAA;AAAA,QACRE,+EAAA;AAAA,QACA;AAAA,UACE,KAAO,EAAA,gBAAA;AAAA,UACP,aAAe,EAAA,qBAAA;AAAA,SACjB;AAAA,OACF,CAAA;AAAA,KACF;AACA,IAAO,OAAA,qBAAA,CAAA;AAAA,WACA,CAAG,EAAA;AACV,IACE,IAAAC,iBAAA;AAAA,MACE,CAAA;AAAA,MACAD,+EAAA;AAAA,KACF;AAEA,MAAM,MAAA,CAAA,CAAA;AACR,IAAA,MAAM,IAAIF,eAAA;AAAA,MACRC,+DAAA;AAAA,MACA,EAAE,OAAO,CAAE,EAAA;AAAA,KACb,CAAA;AAAA,GACF;AACF,CAAA;;;AClIO,SAAS,+BAAgC,CAAA;AAAA,EAC9C,GAAA;AACF,CAAmF,EAAA;AACjF,EAAO,OAAA,eAAe,uCACpB,CAAA,kBAAA,EACA,MACA,EAAA;AACA,IAAA,OAAO,MAAM,wBAAyB,CAAA;AAAA,MACpC,GAAG,MAAA;AAAA,MACH,GAAA;AAAA,MACA,kBAAA;AAAA,KACD,CAAA,CAAA;AAAA,GACH,CAAA;AACF,CAAA;AC3DO,SAAS,qCAAA,CAEd,eAAgC,kBAAyC,EAAA;AACzE,EAAOR,OAAAA,uCAAAA;AAAA,IACL,iCAAA,CAAkC,EAAE,aAAA,EAAe,CAAA;AAAA,IACnD,kBAAA;AAAA,GACF,CAAA;AACF,CAAA;AAoBO,SAAS,4CAAA,CAGd,eAGA,kBACqB,EAAA;AACrB,EAAM,MAAA,gBAAA,GAAmB,CACvBW,sBAEA,KAAA,OAAO,kBAAkB,UACrB,GAAA,aAAA,CAAcA,sBAAqB,CACnC,GAAA,aAAA,CAAA;AACN,EAAM,MAAA,kBAAA,GACJ,uDAAuD,kBAAkB,CAAA,CAAA;AAE3E,EAAA,IAAI,CAAC,kBAAoB,EAAA;AACvB,IAAOX,OAAAA,uCAAAA;AAAA,MACL,iCAAkC,CAAA;AAAA,QAChC,aAAA,EAAe,iBAAiB,IAAI,CAAA;AAAA,OACrC,CAAA;AAAA,MACD,kBAAA;AAAA,KACF,CAAA;AAAA,GACF;AAEA,EAAA,MAAM,EAAE,KAAA,EAAO,aAAe,EAAA,qBAAA,EAA0B,GAAA,kBAAA,CAAA;AACxD,EAAM,MAAA,gBAAA,GAAmB,iBAAiB,qBAAqB,CAAA,CAAA;AAC/D,EAAA,IAAI,qBAAqB,qBAAuB,EAAA;AAC9C,IAAO,OAAA,kBAAA,CAAA;AAAA,GACT;AAEA,EAAA,MAAM,iBAAiB,iCAAkC,CAAA;AAAA,IACvD,aAAe,EAAA,gBAAA;AAAA,GAChB,CAAA,CAAA;AACD,EAAA,MAAM,eAAkB,GAAA,CAAC,GAAG,kBAAA,CAAmB,YAAY,CAAA,CAAA;AAC3D,EAAgB,eAAA,CAAA,MAAA,CAAO,KAAO,EAAA,CAAA,EAAG,cAAc,CAAA,CAAA;AAC/C,EAAA,OAAO,OAAO,MAAO,CAAA;AAAA,IACnB,GAAG,kBAAA;AAAA,IACH,YAAc,EAAA,eAAA;AAAA,GACf,CAAA,CAAA;AACH", "file": "index.js", "sourcesContent": ["/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  containsBytes,\n  getU8Encoder,\n  type Address,\n  type ReadonlyUint8Array,\n} from '@solana/kit';\nimport {\n  type ParsedRequestHeapFrameInstruction,\n  type ParsedRequestUnitsInstruction,\n  type ParsedSetComputeUnitLimitInstruction,\n  type ParsedSetComputeUnitPriceInstruction,\n  type ParsedSetLoadedAccountsDataSizeLimitInstruction,\n} from '../instructions';\n\nexport const COMPUTE_BUDGET_PROGRAM_ADDRESS =\n  'ComputeBudget111111111111111111111111111111' as Address<'ComputeBudget111111111111111111111111111111'>;\n\nexport enum ComputeBudgetInstruction {\n  RequestUnits,\n  RequestHeapFrame,\n  SetComputeUnitLimit,\n  SetComputeUnitPrice,\n  SetLoadedAccountsDataSizeLimit,\n}\n\nexport function identifyComputeBudgetInstruction(\n  instruction: { data: ReadonlyUint8Array } | ReadonlyUint8Array\n): ComputeBudgetInstruction {\n  const data = 'data' in instruction ? instruction.data : instruction;\n  if (containsBytes(data, getU8Encoder().encode(0), 0)) {\n    return ComputeBudgetInstruction.RequestUnits;\n  }\n  if (containsBytes(data, getU8Encoder().encode(1), 0)) {\n    return ComputeBudgetInstruction.RequestHeapFrame;\n  }\n  if (containsBytes(data, getU8Encoder().encode(2), 0)) {\n    return ComputeBudgetInstruction.SetComputeUnitLimit;\n  }\n  if (containsBytes(data, getU8Encoder().encode(3), 0)) {\n    return ComputeBudgetInstruction.SetComputeUnitPrice;\n  }\n  if (containsBytes(data, getU8Encoder().encode(4), 0)) {\n    return ComputeBudgetInstruction.SetLoadedAccountsDataSizeLimit;\n  }\n  throw new Error(\n    'The provided instruction could not be identified as a computeBudget instruction.'\n  );\n}\n\nexport type ParsedComputeBudgetInstruction<\n  TProgram extends string = 'ComputeBudget111111111111111111111111111111',\n> =\n  | ({\n      instructionType: ComputeBudgetInstruction.RequestUnits;\n    } & ParsedRequestUnitsInstruction<TProgram>)\n  | ({\n      instructionType: ComputeBudgetInstruction.RequestHeapFrame;\n    } & ParsedRequestHeapFrameInstruction<TProgram>)\n  | ({\n      instructionType: ComputeBudgetInstruction.SetComputeUnitLimit;\n    } & ParsedSetComputeUnitLimitInstruction<TProgram>)\n  | ({\n      instructionType: ComputeBudgetInstruction.SetComputeUnitPrice;\n    } & ParsedSetComputeUnitPriceInstruction<TProgram>)\n  | ({\n      instructionType: ComputeBudgetInstruction.SetLoadedAccountsDataSizeLimit;\n    } & ParsedSetLoadedAccountsDataSizeLimitInstruction<TProgram>);\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  combineCodec,\n  getStructDecoder,\n  getStructEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  getU8Decoder,\n  getU8Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n} from '@solana/kit';\nimport { COMPUTE_BUDGET_PROGRAM_ADDRESS } from '../programs';\n\nexport const REQUEST_HEAP_FRAME_DISCRIMINATOR = 1;\n\nexport function getRequestHeapFrameDiscriminatorBytes() {\n  return getU8Encoder().encode(REQUEST_HEAP_FRAME_DISCRIMINATOR);\n}\n\nexport type RequestHeapFrameInstruction<\n  TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS,\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<TRemainingAccounts>;\n\nexport type RequestHeapFrameInstructionData = {\n  discriminator: number;\n  /**\n   * Requested transaction-wide program heap size in bytes.\n   * Must be multiple of 1024. Applies to each program, including CPIs.\n   */\n  bytes: number;\n};\n\nexport type RequestHeapFrameInstructionDataArgs = {\n  /**\n   * Requested transaction-wide program heap size in bytes.\n   * Must be multiple of 1024. Applies to each program, including CPIs.\n   */\n  bytes: number;\n};\n\nexport function getRequestHeapFrameInstructionDataEncoder(): Encoder<RequestHeapFrameInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([\n      ['discriminator', getU8Encoder()],\n      ['bytes', getU32Encoder()],\n    ]),\n    (value) => ({ ...value, discriminator: REQUEST_HEAP_FRAME_DISCRIMINATOR })\n  );\n}\n\nexport function getRequestHeapFrameInstructionDataDecoder(): Decoder<RequestHeapFrameInstructionData> {\n  return getStructDecoder([\n    ['discriminator', getU8Decoder()],\n    ['bytes', getU32Decoder()],\n  ]);\n}\n\nexport function getRequestHeapFrameInstructionDataCodec(): Codec<\n  RequestHeapFrameInstructionDataArgs,\n  RequestHeapFrameInstructionData\n> {\n  return combineCodec(\n    getRequestHeapFrameInstructionDataEncoder(),\n    getRequestHeapFrameInstructionDataDecoder()\n  );\n}\n\nexport type RequestHeapFrameInput = {\n  bytes: RequestHeapFrameInstructionDataArgs['bytes'];\n};\n\nexport function getRequestHeapFrameInstruction<\n  TProgramAddress extends Address = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS,\n>(\n  input: RequestHeapFrameInput,\n  config?: { programAddress?: TProgramAddress }\n): RequestHeapFrameInstruction<TProgramAddress> {\n  // Program address.\n  const programAddress =\n    config?.programAddress ?? COMPUTE_BUDGET_PROGRAM_ADDRESS;\n\n  // Original args.\n  const args = { ...input };\n\n  const instruction = {\n    programAddress,\n    data: getRequestHeapFrameInstructionDataEncoder().encode(\n      args as RequestHeapFrameInstructionDataArgs\n    ),\n  } as RequestHeapFrameInstruction<TProgramAddress>;\n\n  return instruction;\n}\n\nexport type ParsedRequestHeapFrameInstruction<\n  TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS,\n> = {\n  programAddress: Address<TProgram>;\n  data: RequestHeapFrameInstructionData;\n};\n\nexport function parseRequestHeapFrameInstruction<TProgram extends string>(\n  instruction: IInstruction<TProgram> & IInstructionWithData<Uint8Array>\n): ParsedRequestHeapFrameInstruction<TProgram> {\n  return {\n    programAddress: instruction.programAddress,\n    data: getRequestHeapFrameInstructionDataDecoder().decode(instruction.data),\n  };\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  combineCodec,\n  getStructDecoder,\n  getStructEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  getU8Decoder,\n  getU8Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n} from '@solana/kit';\nimport { COMPUTE_BUDGET_PROGRAM_ADDRESS } from '../programs';\n\nexport const REQUEST_UNITS_DISCRIMINATOR = 0;\n\nexport function getRequestUnitsDiscriminatorBytes() {\n  return getU8Encoder().encode(REQUEST_UNITS_DISCRIMINATOR);\n}\n\nexport type RequestUnitsInstruction<\n  TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS,\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<TRemainingAccounts>;\n\nexport type RequestUnitsInstructionData = {\n  discriminator: number;\n  /** Units to request for transaction-wide compute. */\n  units: number;\n  /** Prioritization fee lamports. */\n  additionalFee: number;\n};\n\nexport type RequestUnitsInstructionDataArgs = {\n  /** Units to request for transaction-wide compute. */\n  units: number;\n  /** Prioritization fee lamports. */\n  additionalFee: number;\n};\n\nexport function getRequestUnitsInstructionDataEncoder(): Encoder<RequestUnitsInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([\n      ['discriminator', getU8Encoder()],\n      ['units', getU32Encoder()],\n      ['additionalFee', getU32Encoder()],\n    ]),\n    (value) => ({ ...value, discriminator: REQUEST_UNITS_DISCRIMINATOR })\n  );\n}\n\nexport function getRequestUnitsInstructionDataDecoder(): Decoder<RequestUnitsInstructionData> {\n  return getStructDecoder([\n    ['discriminator', getU8Decoder()],\n    ['units', getU32Decoder()],\n    ['additionalFee', getU32Decoder()],\n  ]);\n}\n\nexport function getRequestUnitsInstructionDataCodec(): Codec<\n  RequestUnitsInstructionDataArgs,\n  RequestUnitsInstructionData\n> {\n  return combineCodec(\n    getRequestUnitsInstructionDataEncoder(),\n    getRequestUnitsInstructionDataDecoder()\n  );\n}\n\nexport type RequestUnitsInput = {\n  units: RequestUnitsInstructionDataArgs['units'];\n  additionalFee: RequestUnitsInstructionDataArgs['additionalFee'];\n};\n\nexport function getRequestUnitsInstruction<\n  TProgramAddress extends Address = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS,\n>(\n  input: RequestUnitsInput,\n  config?: { programAddress?: TProgramAddress }\n): RequestUnitsInstruction<TProgramAddress> {\n  // Program address.\n  const programAddress =\n    config?.programAddress ?? COMPUTE_BUDGET_PROGRAM_ADDRESS;\n\n  // Original args.\n  const args = { ...input };\n\n  const instruction = {\n    programAddress,\n    data: getRequestUnitsInstructionDataEncoder().encode(\n      args as RequestUnitsInstructionDataArgs\n    ),\n  } as RequestUnitsInstruction<TProgramAddress>;\n\n  return instruction;\n}\n\nexport type ParsedRequestUnitsInstruction<\n  TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS,\n> = {\n  programAddress: Address<TProgram>;\n  data: RequestUnitsInstructionData;\n};\n\nexport function parseRequestUnitsInstruction<TProgram extends string>(\n  instruction: IInstruction<TProgram> & IInstructionWithData<Uint8Array>\n): ParsedRequestUnitsInstruction<TProgram> {\n  return {\n    programAddress: instruction.programAddress,\n    data: getRequestUnitsInstructionDataDecoder().decode(instruction.data),\n  };\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  combineCodec,\n  getStructDecoder,\n  getStructEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  getU8Decoder,\n  getU8Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n} from '@solana/kit';\nimport { COMPUTE_BUDGET_PROGRAM_ADDRESS } from '../programs';\n\nexport const SET_COMPUTE_UNIT_LIMIT_DISCRIMINATOR = 2;\n\nexport function getSetComputeUnitLimitDiscriminatorBytes() {\n  return getU8Encoder().encode(SET_COMPUTE_UNIT_LIMIT_DISCRIMINATOR);\n}\n\nexport type SetComputeUnitLimitInstruction<\n  TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS,\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<TRemainingAccounts>;\n\nexport type SetComputeUnitLimitInstructionData = {\n  discriminator: number;\n  /** Transaction-wide compute unit limit. */\n  units: number;\n};\n\nexport type SetComputeUnitLimitInstructionDataArgs = {\n  /** Transaction-wide compute unit limit. */\n  units: number;\n};\n\nexport function getSetComputeUnitLimitInstructionDataEncoder(): Encoder<SetComputeUnitLimitInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([\n      ['discriminator', getU8Encoder()],\n      ['units', getU32Encoder()],\n    ]),\n    (value) => ({\n      ...value,\n      discriminator: SET_COMPUTE_UNIT_LIMIT_DISCRIMINATOR,\n    })\n  );\n}\n\nexport function getSetComputeUnitLimitInstructionDataDecoder(): Decoder<SetComputeUnitLimitInstructionData> {\n  return getStructDecoder([\n    ['discriminator', getU8Decoder()],\n    ['units', getU32Decoder()],\n  ]);\n}\n\nexport function getSetComputeUnitLimitInstructionDataCodec(): Codec<\n  SetComputeUnitLimitInstructionDataArgs,\n  SetComputeUnitLimitInstructionData\n> {\n  return combineCodec(\n    getSetComputeUnitLimitInstructionDataEncoder(),\n    getSetComputeUnitLimitInstructionDataDecoder()\n  );\n}\n\nexport type SetComputeUnitLimitInput = {\n  units: SetComputeUnitLimitInstructionDataArgs['units'];\n};\n\nexport function getSetComputeUnitLimitInstruction<\n  TProgramAddress extends Address = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS,\n>(\n  input: SetComputeUnitLimitInput,\n  config?: { programAddress?: TProgramAddress }\n): SetComputeUnitLimitInstruction<TProgramAddress> {\n  // Program address.\n  const programAddress =\n    config?.programAddress ?? COMPUTE_BUDGET_PROGRAM_ADDRESS;\n\n  // Original args.\n  const args = { ...input };\n\n  const instruction = {\n    programAddress,\n    data: getSetComputeUnitLimitInstructionDataEncoder().encode(\n      args as SetComputeUnitLimitInstructionDataArgs\n    ),\n  } as SetComputeUnitLimitInstruction<TProgramAddress>;\n\n  return instruction;\n}\n\nexport type ParsedSetComputeUnitLimitInstruction<\n  TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS,\n> = {\n  programAddress: Address<TProgram>;\n  data: SetComputeUnitLimitInstructionData;\n};\n\nexport function parseSetComputeUnitLimitInstruction<TProgram extends string>(\n  instruction: IInstruction<TProgram> & IInstructionWithData<Uint8Array>\n): ParsedSetComputeUnitLimitInstruction<TProgram> {\n  return {\n    programAddress: instruction.programAddress,\n    data: getSetComputeUnitLimitInstructionDataDecoder().decode(\n      instruction.data\n    ),\n  };\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  combineCodec,\n  getStructDecoder,\n  getStructEncoder,\n  getU64Decoder,\n  getU64Encoder,\n  getU8Decoder,\n  getU8Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n} from '@solana/kit';\nimport { COMPUTE_BUDGET_PROGRAM_ADDRESS } from '../programs';\n\nexport const SET_COMPUTE_UNIT_PRICE_DISCRIMINATOR = 3;\n\nexport function getSetComputeUnitPriceDiscriminatorBytes() {\n  return getU8Encoder().encode(SET_COMPUTE_UNIT_PRICE_DISCRIMINATOR);\n}\n\nexport type SetComputeUnitPriceInstruction<\n  TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS,\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<TRemainingAccounts>;\n\nexport type SetComputeUnitPriceInstructionData = {\n  discriminator: number;\n  /** Transaction compute unit price used for prioritization fees. */\n  microLamports: bigint;\n};\n\nexport type SetComputeUnitPriceInstructionDataArgs = {\n  /** Transaction compute unit price used for prioritization fees. */\n  microLamports: number | bigint;\n};\n\nexport function getSetComputeUnitPriceInstructionDataEncoder(): Encoder<SetComputeUnitPriceInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([\n      ['discriminator', getU8Encoder()],\n      ['microLamports', getU64Encoder()],\n    ]),\n    (value) => ({\n      ...value,\n      discriminator: SET_COMPUTE_UNIT_PRICE_DISCRIMINATOR,\n    })\n  );\n}\n\nexport function getSetComputeUnitPriceInstructionDataDecoder(): Decoder<SetComputeUnitPriceInstructionData> {\n  return getStructDecoder([\n    ['discriminator', getU8Decoder()],\n    ['microLamports', getU64Decoder()],\n  ]);\n}\n\nexport function getSetComputeUnitPriceInstructionDataCodec(): Codec<\n  SetComputeUnitPriceInstructionDataArgs,\n  SetComputeUnitPriceInstructionData\n> {\n  return combineCodec(\n    getSetComputeUnitPriceInstructionDataEncoder(),\n    getSetComputeUnitPriceInstructionDataDecoder()\n  );\n}\n\nexport type SetComputeUnitPriceInput = {\n  microLamports: SetComputeUnitPriceInstructionDataArgs['microLamports'];\n};\n\nexport function getSetComputeUnitPriceInstruction<\n  TProgramAddress extends Address = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS,\n>(\n  input: SetComputeUnitPriceInput,\n  config?: { programAddress?: TProgramAddress }\n): SetComputeUnitPriceInstruction<TProgramAddress> {\n  // Program address.\n  const programAddress =\n    config?.programAddress ?? COMPUTE_BUDGET_PROGRAM_ADDRESS;\n\n  // Original args.\n  const args = { ...input };\n\n  const instruction = {\n    programAddress,\n    data: getSetComputeUnitPriceInstructionDataEncoder().encode(\n      args as SetComputeUnitPriceInstructionDataArgs\n    ),\n  } as SetComputeUnitPriceInstruction<TProgramAddress>;\n\n  return instruction;\n}\n\nexport type ParsedSetComputeUnitPriceInstruction<\n  TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS,\n> = {\n  programAddress: Address<TProgram>;\n  data: SetComputeUnitPriceInstructionData;\n};\n\nexport function parseSetComputeUnitPriceInstruction<TProgram extends string>(\n  instruction: IInstruction<TProgram> & IInstructionWithData<Uint8Array>\n): ParsedSetComputeUnitPriceInstruction<TProgram> {\n  return {\n    programAddress: instruction.programAddress,\n    data: getSetComputeUnitPriceInstructionDataDecoder().decode(\n      instruction.data\n    ),\n  };\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  combineCodec,\n  getStructDecoder,\n  getStructEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  getU8Decoder,\n  getU8Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n} from '@solana/kit';\nimport { COMPUTE_BUDGET_PROGRAM_ADDRESS } from '../programs';\n\nexport const SET_LOADED_ACCOUNTS_DATA_SIZE_LIMIT_DISCRIMINATOR = 4;\n\nexport function getSetLoadedAccountsDataSizeLimitDiscriminatorBytes() {\n  return getU8Encoder().encode(\n    SET_LOADED_ACCOUNTS_DATA_SIZE_LIMIT_DISCRIMINATOR\n  );\n}\n\nexport type SetLoadedAccountsDataSizeLimitInstruction<\n  TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS,\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<TRemainingAccounts>;\n\nexport type SetLoadedAccountsDataSizeLimitInstructionData = {\n  discriminator: number;\n  accountDataSizeLimit: number;\n};\n\nexport type SetLoadedAccountsDataSizeLimitInstructionDataArgs = {\n  accountDataSizeLimit: number;\n};\n\nexport function getSetLoadedAccountsDataSizeLimitInstructionDataEncoder(): Encoder<SetLoadedAccountsDataSizeLimitInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([\n      ['discriminator', getU8Encoder()],\n      ['accountDataSizeLimit', getU32Encoder()],\n    ]),\n    (value) => ({\n      ...value,\n      discriminator: SET_LOADED_ACCOUNTS_DATA_SIZE_LIMIT_DISCRIMINATOR,\n    })\n  );\n}\n\nexport function getSetLoadedAccountsDataSizeLimitInstructionDataDecoder(): Decoder<SetLoadedAccountsDataSizeLimitInstructionData> {\n  return getStructDecoder([\n    ['discriminator', getU8Decoder()],\n    ['accountDataSizeLimit', getU32Decoder()],\n  ]);\n}\n\nexport function getSetLoadedAccountsDataSizeLimitInstructionDataCodec(): Codec<\n  SetLoadedAccountsDataSizeLimitInstructionDataArgs,\n  SetLoadedAccountsDataSizeLimitInstructionData\n> {\n  return combineCodec(\n    getSetLoadedAccountsDataSizeLimitInstructionDataEncoder(),\n    getSetLoadedAccountsDataSizeLimitInstructionDataDecoder()\n  );\n}\n\nexport type SetLoadedAccountsDataSizeLimitInput = {\n  accountDataSizeLimit: SetLoadedAccountsDataSizeLimitInstructionDataArgs['accountDataSizeLimit'];\n};\n\nexport function getSetLoadedAccountsDataSizeLimitInstruction<\n  TProgramAddress extends Address = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS,\n>(\n  input: SetLoadedAccountsDataSizeLimitInput,\n  config?: { programAddress?: TProgramAddress }\n): SetLoadedAccountsDataSizeLimitInstruction<TProgramAddress> {\n  // Program address.\n  const programAddress =\n    config?.programAddress ?? COMPUTE_BUDGET_PROGRAM_ADDRESS;\n\n  // Original args.\n  const args = { ...input };\n\n  const instruction = {\n    programAddress,\n    data: getSetLoadedAccountsDataSizeLimitInstructionDataEncoder().encode(\n      args as SetLoadedAccountsDataSizeLimitInstructionDataArgs\n    ),\n  } as SetLoadedAccountsDataSizeLimitInstruction<TProgramAddress>;\n\n  return instruction;\n}\n\nexport type ParsedSetLoadedAccountsDataSizeLimitInstruction<\n  TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS,\n> = {\n  programAddress: Address<TProgram>;\n  data: SetLoadedAccountsDataSizeLimitInstructionData;\n};\n\nexport function parseSetLoadedAccountsDataSizeLimitInstruction<\n  TProgram extends string,\n>(\n  instruction: IInstruction<TProgram> & IInstructionWithData<Uint8Array>\n): ParsedSetLoadedAccountsDataSizeLimitInstruction<TProgram> {\n  return {\n    programAddress: instruction.programAddress,\n    data: getSetLoadedAccountsDataSizeLimitInstructionDataDecoder().decode(\n      instruction.data\n    ),\n  };\n}\n", "/**\n * A provisory compute unit limit is used to indicate that the transaction\n * should be estimated for compute units before being sent to the network.\n *\n * Setting it to zero ensures the transaction fails unless it is properly estimated.\n */\nexport const PROVISORY_COMPUTE_UNIT_LIMIT = 0;\n\n/**\n * The maximum compute unit limit that can be set for a transaction.\n */\nexport const MAX_COMPUTE_UNIT_LIMIT = 1_400_000;\n", "import {\n  BaseTransactionMessage,\n  getU32D<PERSON>oder,\n  getU64<PERSON><PERSON>oder,\n  IInstruction,\n  MicroLamports,\n  ReadonlyUint8Array,\n} from '@solana/kit';\nimport {\n  COMPUTE_BUDGET_PROGRAM_ADDRESS,\n  ComputeBudgetInstruction,\n  identifyComputeBudgetInstruction,\n  SetComputeUnitLimitInstruction,\n  SetComputeUnitPriceInstruction,\n} from './generated';\n\n/**\n * Finds the index of the first `SetComputeUnitLimit` instruction in a transaction message\n * and its set limit, if any.\n */\nexport function getSetComputeUnitLimitInstructionIndexAndUnits(\n  transactionMessage: BaseTransactionMessage\n): { index: number; units: number } | null {\n  const index = getSetComputeUnitLimitInstructionIndex(transactionMessage);\n  if (index < 0) {\n    return null;\n  }\n\n  const units = getU32Decoder().decode(\n    transactionMessage.instructions[index].data as ReadonlyUint8Array,\n    1\n  );\n\n  return { index, units };\n}\n\n/**\n * Finds the index of the first `SetComputeUnitLimit` instruction in a transaction message, if any.\n */\nexport function getSetComputeUnitLimitInstructionIndex(\n  transactionMessage: BaseTransactionMessage\n) {\n  return transactionMessage.instructions.findIndex(\n    isSetComputeUnitLimitInstruction\n  );\n}\n\n/**\n * Checks if the given instruction is a `SetComputeUnitLimit` instruction.\n */\nexport function isSetComputeUnitLimitInstruction(\n  instruction: IInstruction\n): instruction is SetComputeUnitLimitInstruction {\n  return (\n    instruction.programAddress === COMPUTE_BUDGET_PROGRAM_ADDRESS &&\n    identifyComputeBudgetInstruction(instruction.data as Uint8Array) ===\n      ComputeBudgetInstruction.SetComputeUnitLimit\n  );\n}\n\n/**\n * Finds the index of the first `SetComputeUnitPrice` instruction in a transaction message\n * and its set micro-lamports, if any.\n */\nexport function getSetComputeUnitPriceInstructionIndexAndMicroLamports(\n  transactionMessage: BaseTransactionMessage\n): { index: number; microLamports: MicroLamports } | null {\n  const index = getSetComputeUnitPriceInstructionIndex(transactionMessage);\n  if (index < 0) {\n    return null;\n  }\n\n  const microLamports = getU64Decoder().decode(\n    transactionMessage.instructions[index].data as ReadonlyUint8Array,\n    1\n  ) as MicroLamports;\n\n  return { index, microLamports };\n}\n\n/**\n * Finds the index of the first `SetComputeUnitPrice` instruction in a transaction message, if any.\n */\nexport function getSetComputeUnitPriceInstructionIndex(\n  transactionMessage: BaseTransactionMessage\n) {\n  return transactionMessage.instructions.findIndex(\n    isSetComputeUnitPriceInstruction\n  );\n}\n\n/**\n * Checks if the given instruction is a `SetComputeUnitPrice` instruction.\n */\nexport function isSetComputeUnitPriceInstruction(\n  instruction: IInstruction\n): instruction is SetComputeUnitPriceInstruction {\n  return (\n    instruction.programAddress === COMPUTE_BUDGET_PROGRAM_ADDRESS &&\n    identifyComputeBudgetInstruction(instruction.data as Uint8Array) ===\n      ComputeBudgetInstruction.SetComputeUnitPrice\n  );\n}\n", "import {\n  appendTransactionMessageInstruction,\n  BaseTransactionMessage,\n} from '@solana/kit';\nimport { PROVISORY_COMPUTE_UNIT_LIMIT } from './constants';\nimport { getSetComputeUnitLimitInstruction } from './generated';\nimport { getSetComputeUnitLimitInstructionIndexAndUnits } from './internal';\n\n/**\n * Appends a `SetComputeUnitLimit` instruction with a provisory\n * compute unit limit to a given transaction message\n * if and only if it does not already have one.\n *\n * @example\n * ```ts\n * const transactionMessage = pipe(\n *   createTransactionMessage({ version: 0 }),\n *   fillProvisorySetComputeUnitLimitInstruction,\n *   // ...\n * );\n * ```\n */\nexport function fillProvisorySetComputeUnitLimitInstruction<\n  TTransactionMessage extends BaseTransactionMessage,\n>(transactionMessage: TTransactionMessage) {\n  return updateOrAppendSetComputeUnitLimitInstruction(\n    (previousUnits) =>\n      previousUnits === null ? PROVISORY_COMPUTE_UNIT_LIMIT : previousUnits,\n    transactionMessage\n  );\n}\n\n/**\n * Updates the first `SetComputeUnitLimit` instruction in a transaction message\n * with the given units, or appends a new instruction if none exists.\n * A function of the current value can be provided instead of a static value.\n *\n * @param units - The new compute unit limit, or a function that takes the previous\n *                compute unit limit and returns the new limit.\n * @param transactionMessage - The transaction message to update.\n *\n * @example\n * ```ts\n * const updatedTransactionMessage = updateOrAppendSetComputeUnitLimitInstruction(\n *   // E.g. Keep the current limit if it is set, otherwise set it to the maximum.\n *   (currentUnits) => currentUnits === null ? MAX_COMPUTE_UNIT_LIMIT : currentUnits,\n *   transactionMessage,\n * );\n * ```\n */\nexport function updateOrAppendSetComputeUnitLimitInstruction<\n  TTransactionMessage extends BaseTransactionMessage,\n>(\n  units: number | ((previousUnits: number | null) => number),\n  transactionMessage: TTransactionMessage\n): TTransactionMessage {\n  const getUnits = (previousUnits: number | null): number =>\n    typeof units === 'function' ? units(previousUnits) : units;\n  const instructionDetails =\n    getSetComputeUnitLimitInstructionIndexAndUnits(transactionMessage);\n\n  if (!instructionDetails) {\n    return appendTransactionMessageInstruction(\n      getSetComputeUnitLimitInstruction({ units: getUnits(null) }),\n      transactionMessage\n    );\n  }\n\n  const { index, units: previousUnits } = instructionDetails;\n  const newUnits = getUnits(previousUnits);\n  if (newUnits === previousUnits) {\n    return transactionMessage;\n  }\n\n  const newInstruction = getSetComputeUnitLimitInstruction({ units: newUnits });\n  const newInstructions = [...transactionMessage.instructions];\n  newInstructions.splice(index, 1, newInstruction);\n  return Object.freeze({\n    ...transactionMessage,\n    instructions: newInstructions,\n  });\n}\n", "import {\n  CompilableTransactionMessage,\n  ITransactionMessageWithFeePayer,\n  TransactionMessage,\n} from '@solana/kit';\nimport {\n  MAX_COMPUTE_UNIT_LIMIT,\n  PROVISORY_COMPUTE_UNIT_LIMIT,\n} from './constants';\nimport {\n  EstimateComputeUnitLimitFactoryFunction,\n  EstimateComputeUnitLimitFactoryFunctionConfig,\n} from './estimateComputeLimitInternal';\nimport { getSetComputeUnitLimitInstructionIndexAndUnits } from './internal';\nimport { updateOrAppendSetComputeUnitLimitInstruction } from './setComputeLimit';\n\ntype EstimateAndUpdateProvisoryComputeUnitLimitFactoryFunction = <\n  TTransactionMessage extends\n    | CompilableTransactionMessage\n    | (TransactionMessage & ITransactionMessageWithFeePayer),\n>(\n  transactionMessage: TTransactionMessage,\n  config?: EstimateComputeUnitLimitFactoryFunctionConfig\n) => Promise<TTransactionMessage>;\n\n/**\n * Given a transaction message, if it does not have an explicit compute unit limit,\n * estimates the compute unit limit and updates the transaction message with\n * the estimated limit. Otherwise, returns the transaction message unchanged.\n *\n * It requires a function that estimates the compute unit limit.\n *\n * @example\n * ```ts\n * const estimateAndUpdateCUs = estimateAndUpdateProvisoryComputeUnitLimitFactory(\n *     estimateComputeUnitLimitFactory({ rpc })\n * );\n *\n * const transactionMessageWithCUs = await estimateAndUpdateCUs(transactionMessage);\n * ```\n *\n * @see {@link estimateAndUpdateProvisoryComputeUnitLimitFactory}\n */\nexport function estimateAndUpdateProvisoryComputeUnitLimitFactory(\n  estimateComputeUnitLimit: EstimateComputeUnitLimitFactoryFunction\n): EstimateAndUpdateProvisoryComputeUnitLimitFactoryFunction {\n  return async function fn(transactionMessage, config) {\n    const instructionDetails =\n      getSetComputeUnitLimitInstructionIndexAndUnits(transactionMessage);\n\n    // If the transaction message already has a compute unit limit instruction\n    // which is set to a specific value — i.e. not 0 or the maximum limit —\n    // we don't need to estimate the compute unit limit.\n    if (\n      instructionDetails &&\n      instructionDetails.units !== PROVISORY_COMPUTE_UNIT_LIMIT &&\n      instructionDetails.units !== MAX_COMPUTE_UNIT_LIMIT\n    ) {\n      return transactionMessage;\n    }\n\n    return updateOrAppendSetComputeUnitLimitInstruction(\n      await estimateComputeUnitLimit(transactionMessage, config),\n      transactionMessage\n    );\n  };\n}\n", "// TODO: Add these helpers to @solana/kit in v3.\n\nimport {\n  BaseTransactionMessage,\n  Blockhash,\n  setTransactionMessageLifetimeUsingBlockhash,\n  TransactionMessageWithBlockhashLifetime,\n  TransactionMessageWithDurableNonceLifetime,\n} from '@solana/kit';\n\n/**\n * An invalid blockhash lifetime constraint used as a placeholder for\n * transaction messages that are not yet ready to be compiled.\n *\n * This enables various operations on the transaction message, such as\n * simulating it or calculating its transaction size, whilst defering\n * the actual blockhash to a later stage.\n */\nexport const PROVISORY_BLOCKHASH_LIFETIME_CONSTRAINT: TransactionMessageWithBlockhashLifetime['lifetimeConstraint'] =\n  {\n    blockhash: '11111111111111111111111111111111' as Blockhash,\n    lastValidBlockHeight: 0n, // This is not included in compiled transactions; it can be anything.\n  };\n\n/**\n * Sets a provisory blockhash lifetime constraint on the transaction message\n * if and only if it doesn't already have a lifetime constraint.\n */\nexport function fillMissingTransactionMessageLifetimeUsingProvisoryBlockhash<\n  TTransactionMessage extends BaseTransactionMessage,\n>(\n  transactionMessage: TTransactionMessage\n): TTransactionMessage &\n  (\n    | TransactionMessageWithBlockhashLifetime\n    | TransactionMessageWithDurableNonceLifetime\n  ) {\n  type ReturnType = TTransactionMessage &\n    (\n      | TransactionMessageWithBlockhashLifetime\n      | TransactionMessageWithDurableNonceLifetime\n    );\n\n  if ('lifetimeConstraint' in transactionMessage) {\n    return transactionMessage as ReturnType;\n  }\n\n  return setTransactionMessageLifetimeUsingProvisoryBlockhash(\n    transactionMessage\n  );\n}\n\n/**\n * Sets a provisory blockhash lifetime constraint on the transaction message.\n */\nexport function setTransactionMessageLifetimeUsingProvisoryBlockhash<\n  TTransactionMessage extends BaseTransactionMessage,\n>(\n  transactionMessage: TTransactionMessage\n): TTransactionMessage & TransactionMessageWithBlockhashLifetime {\n  return setTransactionMessageLifetimeUsingBlockhash(\n    PROVISORY_BLOCKHASH_LIFETIME_CONSTRAINT,\n    transactionMessage\n  );\n}\n", "import {\n  Commitment,\n  CompilableTransactionMessage,\n  compileTransaction,\n  getBase64EncodedWireTransaction,\n  isDurableNonceTransaction,\n  isSolanaError,\n  ITransactionMessageWithFeePayer,\n  pipe,\n  Rpc,\n  SimulateTransactionApi,\n  Slot,\n  SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT,\n  SOLANA_ERROR__TRANSACTION__FAILED_WHEN_SIMULATING_TO_ESTIMATE_COMPUTE_LIMIT,\n  SolanaError,\n  Transaction,\n  TransactionMessage,\n} from '@solana/kit';\nimport { updateOrAppendSetComputeUnitLimitInstruction } from './setComputeLimit';\nimport { MAX_COMPUTE_UNIT_LIMIT } from './constants';\nimport { fillMissingTransactionMessageLifetimeUsingProvisoryBlockhash } from './internalMoveToKit';\n\nexport type EstimateComputeUnitLimitFactoryConfig = Readonly<{\n  /** An object that supports the {@link SimulateTransactionApi} of the Solana RPC API */\n  rpc: Rpc<SimulateTransactionApi>;\n}>;\n\nexport type EstimateComputeUnitLimitFactoryFunction = (\n  transactionMessage:\n    | CompilableTransactionMessage\n    | (TransactionMessage & ITransactionMessageWithFeePayer),\n  config?: EstimateComputeUnitLimitFactoryFunctionConfig\n) => Promise<number>;\n\nexport type EstimateComputeUnitLimitFactoryFunctionConfig = {\n  abortSignal?: AbortSignal;\n  /**\n   * Compute the estimate as of the highest slot that has reached this level of commitment.\n   *\n   * @defaultValue Whichever default is applied by the underlying {@link RpcApi} in use. For\n   * example, when using an API created by a `createSolanaRpc*()` helper, the default commitment\n   * is `\"confirmed\"` unless configured otherwise. Unmitigated by an API layer on the client, the\n   * default commitment applied by the server is `\"finalized\"`.\n   */\n  commitment?: Commitment;\n  /**\n   * Prevents accessing stale data by enforcing that the RPC node has processed transactions up to\n   * this slot\n   */\n  minContextSlot?: Slot;\n};\n\ntype EstimateComputeUnitLimitConfig =\n  EstimateComputeUnitLimitFactoryFunctionConfig &\n    Readonly<{\n      rpc: Rpc<SimulateTransactionApi>;\n      transactionMessage:\n        | CompilableTransactionMessage\n        | (TransactionMessage & ITransactionMessageWithFeePayer);\n    }>;\n\n/**\n * Simulates a transaction message on the network and returns the number of compute units it\n * consumed during simulation.\n *\n * The estimate this function returns can be used to set a compute unit limit on the transaction.\n * Correctly budgeting a compute unit limit for your transaction message can increase the probability\n * that your transaction will be accepted for processing.\n *\n * If you don't declare a compute unit limit on your transaction, validators will assume an upper\n * limit of 200K compute units (CU) per instruction. Since validators have an incentive to pack as\n * many transactions into each block as possible, they may choose to include transactions that they\n * know will fit into the remaining compute budget for the current block over transactions that\n * might not. For this reason, you should set a compute unit limit on each of your transaction\n * messages, whenever possible.\n *\n * ## Example\n *\n * ```ts\n * import { getSetComputeLimitInstruction } from '@solana-program/compute-budget';\n * import { createSolanaRpc, getComputeUnitEstimateForTransactionMessageFactory, pipe } from '@solana/kit';\n *\n * // Create an estimator function.\n * const rpc = createSolanaRpc('http://127.0.0.1:8899');\n * const getComputeUnitEstimateForTransactionMessage =\n *     getComputeUnitEstimateForTransactionMessageFactory({ rpc });\n *\n * // Create your transaction message.\n * const transactionMessage = pipe(\n *     createTransactionMessage({ version: 'legacy' }),\n *     /* ... *\\/\n * );\n *\n * // Request an estimate of the actual compute units this message will consume.\n * const computeUnitsEstimate =\n *     await getComputeUnitEstimateForTransactionMessage(transactionMessage);\n *\n * // Set the transaction message's compute unit budget.\n * const transactionMessageWithComputeUnitLimit = prependTransactionMessageInstruction(\n *     getSetComputeLimitInstruction({ units: computeUnitsEstimate }),\n *     transactionMessage,\n * );\n * ```\n *\n * > [!WARNING]\n * > The compute unit estimate is just that &ndash; an estimate. The compute unit consumption of the\n * > actual transaction might be higher or lower than what was observed in simulation. Unless you\n * > are confident that your particular transaction message will consume the same or fewer compute\n * > units as was estimated, you might like to augment the estimate by either a fixed number of CUs\n * > or a multiplier.\n *\n * > [!NOTE]\n * > If you are preparing an _unsigned_ transaction, destined to be signed and submitted to the\n * > network by a wallet, you might like to leave it up to the wallet to determine the compute unit\n * > limit. Consider that the wallet might have a more global view of how many compute units certain\n * > types of transactions consume, and might be able to make better estimates of an appropriate\n * > compute unit budget.\n */\nexport async function estimateComputeUnitLimit({\n  transactionMessage,\n  ...configs\n}: EstimateComputeUnitLimitConfig): Promise<number> {\n  const replaceRecentBlockhash = !isDurableNonceTransaction(transactionMessage);\n  const transaction = pipe(\n    transactionMessage,\n    fillMissingTransactionMessageLifetimeUsingProvisoryBlockhash,\n    (m) =>\n      updateOrAppendSetComputeUnitLimitInstruction(MAX_COMPUTE_UNIT_LIMIT, m),\n    compileTransaction\n  );\n\n  return await simulateTransactionAndGetConsumedUnits({\n    transaction,\n    replaceRecentBlockhash,\n    ...configs,\n  });\n}\n\ntype SimulateTransactionAndGetConsumedUnitsConfig = Omit<\n  EstimateComputeUnitLimitConfig,\n  'transactionMessage'\n> &\n  Readonly<{ replaceRecentBlockhash?: boolean; transaction: Transaction }>;\n\nasync function simulateTransactionAndGetConsumedUnits({\n  abortSignal,\n  rpc,\n  transaction,\n  ...simulateConfig\n}: SimulateTransactionAndGetConsumedUnitsConfig): Promise<number> {\n  const wireTransactionBytes = getBase64EncodedWireTransaction(transaction);\n\n  try {\n    const {\n      value: { err: transactionError, unitsConsumed },\n    } = await rpc\n      .simulateTransaction(wireTransactionBytes, {\n        ...simulateConfig,\n        encoding: 'base64',\n        sigVerify: false,\n      })\n      .send({ abortSignal });\n    if (unitsConsumed == null) {\n      // This should never be hit, because all RPCs should support `unitsConsumed` by now.\n      throw new SolanaError(\n        SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT\n      );\n    }\n    // FIXME(https://github.com/anza-xyz/agave/issues/1295): The simulation response returns\n    // compute units as a u64, but the `SetComputeLimit` instruction only accepts a u32. Until\n    // this changes, downcast it.\n    const downcastUnitsConsumed =\n      unitsConsumed > 4_294_967_295n ? 4_294_967_295 : Number(unitsConsumed);\n    if (transactionError) {\n      throw new SolanaError(\n        SOLANA_ERROR__TRANSACTION__FAILED_WHEN_SIMULATING_TO_ESTIMATE_COMPUTE_LIMIT,\n        {\n          cause: transactionError,\n          unitsConsumed: downcastUnitsConsumed,\n        }\n      );\n    }\n    return downcastUnitsConsumed;\n  } catch (e) {\n    if (\n      isSolanaError(\n        e,\n        SOLANA_ERROR__TRANSACTION__FAILED_WHEN_SIMULATING_TO_ESTIMATE_COMPUTE_LIMIT\n      )\n    )\n      throw e;\n    throw new SolanaError(\n      SOLANA_ERROR__TRANSACTION__FAILED_TO_ESTIMATE_COMPUTE_LIMIT,\n      { cause: e }\n    );\n  }\n}\n", "import {\n  estimateComputeUnitLimit,\n  EstimateComputeUnitLimitFactoryConfig,\n  EstimateComputeUnitLimitFactoryFunction,\n} from './estimateComputeLimitInternal';\n\n/**\n * Use this utility to estimate the actual compute unit cost of a given transaction message.\n *\n * Correctly budgeting a compute unit limit for your transaction message can increase the\n * probability that your transaction will be accepted for processing. If you don't declare a compute\n * unit limit on your transaction, validators will assume an upper limit of 200K compute units (CU)\n * per instruction.\n *\n * Since validators have an incentive to pack as many transactions into each block as possible, they\n * may choose to include transactions that they know will fit into the remaining compute budget for\n * the current block over transactions that might not. For this reason, you should set a compute\n * unit limit on each of your transaction messages, whenever possible.\n *\n * > [!WARNING]\n * > The compute unit estimate is just that -- an estimate. The compute unit consumption of the\n * > actual transaction might be higher or lower than what was observed in simulation. Unless you\n * > are confident that your particular transaction message will consume the same or fewer compute\n * > units as was estimated, you might like to augment the estimate by either a fixed number of CUs\n * > or a multiplier.\n *\n * > [!NOTE]\n * > If you are preparing an _unsigned_ transaction, destined to be signed and submitted to the\n * > network by a wallet, you might like to leave it up to the wallet to determine the compute unit\n * > limit. Consider that the wallet might have a more global view of how many compute units certain\n * > types of transactions consume, and might be able to make better estimates of an appropriate\n * > compute unit budget.\n *\n * > [!INFO]\n * > In the event that a transaction message does not already have a `SetComputeUnitLimit`\n * > instruction, this function will add one before simulation. This ensures that the compute unit\n * > consumption of the `SetComputeUnitLimit` instruction itself is included in the estimate.\n *\n * @param config\n *\n * @example\n * ```ts\n * import { getSetComputeUnitLimitInstruction } from '@solana-program/compute-budget';\n * import { createSolanaRpc, estimateComputeUnitLimitFactory, pipe } from '@solana/kit';\n *\n * // Create an estimator function.\n * const rpc = createSolanaRpc('http://127.0.0.1:8899');\n * const estimateComputeUnitLimit = estimateComputeUnitLimitFactory({ rpc });\n *\n * // Create your transaction message.\n * const transactionMessage = pipe(\n *     createTransactionMessage({ version: 'legacy' }),\n *     /* ... *\\/\n * );\n *\n * // Request an estimate of the actual compute units this message will consume. This is done by\n * // simulating the transaction and grabbing the estimated compute units from the result.\n * const estimatedUnits = await estimateComputeUnitLimit(transactionMessage);\n *\n * // Set the transaction message's compute unit budget.\n * const transactionMessageWithComputeUnitLimit = prependTransactionMessageInstruction(\n *     getSetComputeUnitLimitInstruction({ units: estimatedUnits }),\n *     transactionMessage,\n * );\n * ```\n */\nexport function estimateComputeUnitLimitFactory({\n  rpc,\n}: EstimateComputeUnitLimitFactoryConfig): EstimateComputeUnitLimitFactoryFunction {\n  return async function estimateComputeUnitLimitFactoryFunction(\n    transactionMessage,\n    config\n  ) {\n    return await estimateComputeUnitLimit({\n      ...config,\n      rpc,\n      transactionMessage,\n    });\n  };\n}\n", "import {\n  appendTransactionMessageInstruction,\n  BaseTransactionMessage,\n  MicroLamports,\n} from '@solana/kit';\nimport { getSetComputeUnitPriceInstruction } from './generated';\nimport { getSetComputeUnitPriceInstructionIndexAndMicroLamports } from './internal';\n\n/**\n * Sets the compute unit price of a transaction message in micro-Lamports.\n *\n * @example\n * ```ts\n * const transactionMessage = pipe(\n *   createTransactionMessage({ version: 0 }),\n *   (m) => setTransactionMessageComputeUnitPrice(10_000, m),\n *   // ...\n * );\n * ```\n */\nexport function setTransactionMessageComputeUnitPrice<\n  TTransactionMessage extends BaseTransactionMessage,\n>(microLamports: number | bigint, transactionMessage: TTransactionMessage) {\n  return appendTransactionMessageInstruction(\n    getSetComputeUnitPriceInstruction({ microLamports }),\n    transactionMessage\n  );\n}\n\n/**\n * Updates the first `SetComputeUnitPrice` instruction in a transaction message\n * with the given micro-Lamports, or appends a new instruction if none exists.\n * A function of the current value can be provided instead of a static value.\n *\n * @param microLamports - The new compute unit price, or a function that\n *                        takes the previous price and returns the new one.\n * @param transactionMessage - The transaction message to update.\n *\n * @example\n * ```ts\n * const updatedTransactionMessage = updateOrAppendSetComputeUnitPriceInstruction(\n *   // E.g. double the current price or set it to 10_000 if it isn't set.\n *   (currentPrice) => currentPrice === null ? 10_000 : currentPrice * 2,\n *   transactionMessage,\n * );\n * ```\n */\nexport function updateOrAppendSetComputeUnitPriceInstruction<\n  TTransactionMessage extends BaseTransactionMessage,\n>(\n  microLamports:\n    | MicroLamports\n    | ((previousMicroLamports: MicroLamports | null) => MicroLamports),\n  transactionMessage: TTransactionMessage\n): TTransactionMessage {\n  const getMicroLamports = (\n    previousMicroLamports: MicroLamports | null\n  ): MicroLamports =>\n    typeof microLamports === 'function'\n      ? microLamports(previousMicroLamports)\n      : microLamports;\n  const instructionDetails =\n    getSetComputeUnitPriceInstructionIndexAndMicroLamports(transactionMessage);\n\n  if (!instructionDetails) {\n    return appendTransactionMessageInstruction(\n      getSetComputeUnitPriceInstruction({\n        microLamports: getMicroLamports(null),\n      }),\n      transactionMessage\n    );\n  }\n\n  const { index, microLamports: previousMicroLamports } = instructionDetails;\n  const newMicroLamports = getMicroLamports(previousMicroLamports);\n  if (newMicroLamports === previousMicroLamports) {\n    return transactionMessage;\n  }\n\n  const newInstruction = getSetComputeUnitPriceInstruction({\n    microLamports: newMicroLamports,\n  });\n  const newInstructions = [...transactionMessage.instructions];\n  newInstructions.splice(index, 1, newInstruction);\n  return Object.freeze({\n    ...transactionMessage,\n    instructions: newInstructions,\n  });\n}\n"]}