/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type IAccountMeta, type IAccountSignerMeta, type ProgramDerivedAddress, type TransactionSigner } from '@solana/kit';
/**
 * Asserts that the given value is not null or undefined.
 * @internal
 */
export declare function expectSome<T>(value: T | null | undefined): T;
/**
 * Asserts that the given value is a PublicKey.
 * @internal
 */
export declare function expectAddress<T extends string = string>(value: Address<T> | ProgramDerivedAddress<T> | TransactionSigner<T> | null | undefined): Address<T>;
/**
 * Asserts that the given value is a PDA.
 * @internal
 */
export declare function expectProgramDerivedAddress<T extends string = string>(value: Address<T> | ProgramDerivedAddress<T> | TransactionSigner<T> | null | undefined): ProgramDerivedAddress<T>;
/**
 * Asserts that the given value is a TransactionSigner.
 * @internal
 */
export declare function expectTransactionSigner<T extends string = string>(value: Address<T> | ProgramDerivedAddress<T> | TransactionSigner<T> | null | undefined): TransactionSigner<T>;
/**
 * Defines an instruction account to resolve.
 * @internal
 */
export type ResolvedAccount<T extends string = string, U extends Address<T> | ProgramDerivedAddress<T> | TransactionSigner<T> | null = Address<T> | ProgramDerivedAddress<T> | TransactionSigner<T> | null> = {
    isWritable: boolean;
    value: U;
};
/**
 * Defines an instruction that stores additional bytes on-chain.
 * @internal
 */
export type IInstructionWithByteDelta = {
    byteDelta: number;
};
/**
 * Get account metas and signers from resolved accounts.
 * @internal
 */
export declare function getAccountMetaFactory(programAddress: Address, optionalAccountStrategy: 'omitted' | 'programId'): (account: ResolvedAccount) => IAccountMeta | IAccountSignerMeta | undefined;
export declare function isTransactionSigner<TAddress extends string = string>(value: Address<TAddress> | ProgramDerivedAddress<TAddress> | TransactionSigner<TAddress>): value is TransactionSigner<TAddress>;
//# sourceMappingURL=index.d.ts.map