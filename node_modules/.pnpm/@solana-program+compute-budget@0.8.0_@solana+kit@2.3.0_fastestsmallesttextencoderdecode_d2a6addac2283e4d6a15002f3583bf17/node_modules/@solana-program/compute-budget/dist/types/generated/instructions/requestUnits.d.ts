/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData } from '@solana/kit';
import { COMPUTE_BUDGET_PROGRAM_ADDRESS } from '../programs';
export declare const REQUEST_UNITS_DISCRIMINATOR = 0;
export declare function getRequestUnitsDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type RequestUnitsInstruction<TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS, TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<TRemainingAccounts>;
export type RequestUnitsInstructionData = {
    discriminator: number;
    /** Units to request for transaction-wide compute. */
    units: number;
    /** Prioritization fee lamports. */
    additionalFee: number;
};
export type RequestUnitsInstructionDataArgs = {
    /** Units to request for transaction-wide compute. */
    units: number;
    /** Prioritization fee lamports. */
    additionalFee: number;
};
export declare function getRequestUnitsInstructionDataEncoder(): Encoder<RequestUnitsInstructionDataArgs>;
export declare function getRequestUnitsInstructionDataDecoder(): Decoder<RequestUnitsInstructionData>;
export declare function getRequestUnitsInstructionDataCodec(): Codec<RequestUnitsInstructionDataArgs, RequestUnitsInstructionData>;
export type RequestUnitsInput = {
    units: RequestUnitsInstructionDataArgs['units'];
    additionalFee: RequestUnitsInstructionDataArgs['additionalFee'];
};
export declare function getRequestUnitsInstruction<TProgramAddress extends Address = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS>(input: RequestUnitsInput, config?: {
    programAddress?: TProgramAddress;
}): RequestUnitsInstruction<TProgramAddress>;
export type ParsedRequestUnitsInstruction<TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS> = {
    programAddress: Address<TProgram>;
    data: RequestUnitsInstructionData;
};
export declare function parseRequestUnitsInstruction<TProgram extends string>(instruction: IInstruction<TProgram> & IInstructionWithData<Uint8Array>): ParsedRequestUnitsInstruction<TProgram>;
//# sourceMappingURL=requestUnits.d.ts.map