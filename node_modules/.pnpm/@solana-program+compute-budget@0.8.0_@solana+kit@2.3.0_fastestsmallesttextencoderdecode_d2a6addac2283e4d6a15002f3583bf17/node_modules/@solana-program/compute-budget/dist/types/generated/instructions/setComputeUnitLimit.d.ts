/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData } from '@solana/kit';
import { COMPUTE_BUDGET_PROGRAM_ADDRESS } from '../programs';
export declare const SET_COMPUTE_UNIT_LIMIT_DISCRIMINATOR = 2;
export declare function getSetComputeUnitLimitDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type SetComputeUnitLimitInstruction<TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS, TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<TRemainingAccounts>;
export type SetComputeUnitLimitInstructionData = {
    discriminator: number;
    /** Transaction-wide compute unit limit. */
    units: number;
};
export type SetComputeUnitLimitInstructionDataArgs = {
    /** Transaction-wide compute unit limit. */
    units: number;
};
export declare function getSetComputeUnitLimitInstructionDataEncoder(): Encoder<SetComputeUnitLimitInstructionDataArgs>;
export declare function getSetComputeUnitLimitInstructionDataDecoder(): Decoder<SetComputeUnitLimitInstructionData>;
export declare function getSetComputeUnitLimitInstructionDataCodec(): Codec<SetComputeUnitLimitInstructionDataArgs, SetComputeUnitLimitInstructionData>;
export type SetComputeUnitLimitInput = {
    units: SetComputeUnitLimitInstructionDataArgs['units'];
};
export declare function getSetComputeUnitLimitInstruction<TProgramAddress extends Address = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS>(input: SetComputeUnitLimitInput, config?: {
    programAddress?: TProgramAddress;
}): SetComputeUnitLimitInstruction<TProgramAddress>;
export type ParsedSetComputeUnitLimitInstruction<TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS> = {
    programAddress: Address<TProgram>;
    data: SetComputeUnitLimitInstructionData;
};
export declare function parseSetComputeUnitLimitInstruction<TProgram extends string>(instruction: IInstruction<TProgram> & IInstructionWithData<Uint8Array>): ParsedSetComputeUnitLimitInstruction<TProgram>;
//# sourceMappingURL=setComputeUnitLimit.d.ts.map