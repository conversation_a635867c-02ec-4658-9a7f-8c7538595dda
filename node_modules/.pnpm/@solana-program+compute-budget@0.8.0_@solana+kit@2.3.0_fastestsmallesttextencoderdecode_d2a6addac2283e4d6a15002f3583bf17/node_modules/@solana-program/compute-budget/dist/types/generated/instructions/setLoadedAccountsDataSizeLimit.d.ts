/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData } from '@solana/kit';
import { COMPUTE_BUDGET_PROGRAM_ADDRESS } from '../programs';
export declare const SET_LOADED_ACCOUNTS_DATA_SIZE_LIMIT_DISCRIMINATOR = 4;
export declare function getSetLoadedAccountsDataSizeLimitDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type SetLoadedAccountsDataSizeLimitInstruction<TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS, TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<TRemainingAccounts>;
export type SetLoadedAccountsDataSizeLimitInstructionData = {
    discriminator: number;
    accountDataSizeLimit: number;
};
export type SetLoadedAccountsDataSizeLimitInstructionDataArgs = {
    accountDataSizeLimit: number;
};
export declare function getSetLoadedAccountsDataSizeLimitInstructionDataEncoder(): Encoder<SetLoadedAccountsDataSizeLimitInstructionDataArgs>;
export declare function getSetLoadedAccountsDataSizeLimitInstructionDataDecoder(): Decoder<SetLoadedAccountsDataSizeLimitInstructionData>;
export declare function getSetLoadedAccountsDataSizeLimitInstructionDataCodec(): Codec<SetLoadedAccountsDataSizeLimitInstructionDataArgs, SetLoadedAccountsDataSizeLimitInstructionData>;
export type SetLoadedAccountsDataSizeLimitInput = {
    accountDataSizeLimit: SetLoadedAccountsDataSizeLimitInstructionDataArgs['accountDataSizeLimit'];
};
export declare function getSetLoadedAccountsDataSizeLimitInstruction<TProgramAddress extends Address = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS>(input: SetLoadedAccountsDataSizeLimitInput, config?: {
    programAddress?: TProgramAddress;
}): SetLoadedAccountsDataSizeLimitInstruction<TProgramAddress>;
export type ParsedSetLoadedAccountsDataSizeLimitInstruction<TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS> = {
    programAddress: Address<TProgram>;
    data: SetLoadedAccountsDataSizeLimitInstructionData;
};
export declare function parseSetLoadedAccountsDataSizeLimitInstruction<TProgram extends string>(instruction: IInstruction<TProgram> & IInstructionWithData<Uint8Array>): ParsedSetLoadedAccountsDataSizeLimitInstruction<TProgram>;
//# sourceMappingURL=setLoadedAccountsDataSizeLimit.d.ts.map