{"version": 3, "file": "requestHeapFrame.d.ts", "sourceRoot": "", "sources": ["../../../../src/generated/instructions/requestHeapFrame.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,EASL,KAAK,OAAO,EACZ,KAAK,KAAK,EACV,KAAK,OAAO,EACZ,KAAK,OAAO,EACZ,KAAK,YAAY,EACjB,KAAK,YAAY,EACjB,KAAK,wBAAwB,EAC7B,KAAK,oBAAoB,EAC1B,MAAM,aAAa,CAAC;AACrB,OAAO,EAAE,8BAA8B,EAAE,MAAM,aAAa,CAAC;AAE7D,eAAO,MAAM,gCAAgC,IAAI,CAAC;AAElD,wBAAgB,qCAAqC,6CAEpD;AAED,MAAM,MAAM,2BAA2B,CACrC,QAAQ,SAAS,MAAM,GAAG,OAAO,8BAA8B,EAC/D,kBAAkB,SAAS,SAAS,YAAY,CAAC,MAAM,CAAC,EAAE,GAAG,EAAE,IAC7D,YAAY,CAAC,QAAQ,CAAC,GACxB,oBAAoB,CAAC,UAAU,CAAC,GAChC,wBAAwB,CAAC,kBAAkB,CAAC,CAAC;AAE/C,MAAM,MAAM,+BAA+B,GAAG;IAC5C,aAAa,EAAE,MAAM,CAAC;IACtB;;;OAGG;IACH,KAAK,EAAE,MAAM,CAAC;CACf,CAAC;AAEF,MAAM,MAAM,mCAAmC,GAAG;IAChD;;;OAGG;IACH,KAAK,EAAE,MAAM,CAAC;CACf,CAAC;AAEF,wBAAgB,yCAAyC,IAAI,OAAO,CAAC,mCAAmC,CAAC,CAQxG;AAED,wBAAgB,yCAAyC,IAAI,OAAO,CAAC,+BAA+B,CAAC,CAKpG;AAED,wBAAgB,uCAAuC,IAAI,KAAK,CAC9D,mCAAmC,EACnC,+BAA+B,CAChC,CAKA;AAED,MAAM,MAAM,qBAAqB,GAAG;IAClC,KAAK,EAAE,mCAAmC,CAAC,OAAO,CAAC,CAAC;CACrD,CAAC;AAEF,wBAAgB,8BAA8B,CAC5C,eAAe,SAAS,OAAO,GAAG,OAAO,8BAA8B,EAEvE,KAAK,EAAE,qBAAqB,EAC5B,MAAM,CAAC,EAAE;IAAE,cAAc,CAAC,EAAE,eAAe,CAAA;CAAE,GAC5C,2BAA2B,CAAC,eAAe,CAAC,CAgB9C;AAED,MAAM,MAAM,iCAAiC,CAC3C,QAAQ,SAAS,MAAM,GAAG,OAAO,8BAA8B,IAC7D;IACF,cAAc,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;IAClC,IAAI,EAAE,+BAA+B,CAAC;CACvC,CAAC;AAEF,wBAAgB,gCAAgC,CAAC,QAAQ,SAAS,MAAM,EACtE,WAAW,EAAE,YAAY,CAAC,QAAQ,CAAC,GAAG,oBAAoB,CAAC,UAAU,CAAC,GACrE,iCAAiC,CAAC,QAAQ,CAAC,CAK7C"}