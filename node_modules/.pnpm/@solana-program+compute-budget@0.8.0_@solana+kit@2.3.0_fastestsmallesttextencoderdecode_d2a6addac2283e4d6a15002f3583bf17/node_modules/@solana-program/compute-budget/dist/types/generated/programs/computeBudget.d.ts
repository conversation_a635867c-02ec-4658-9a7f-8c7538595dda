/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type ReadonlyUint8Array } from '@solana/kit';
import { type ParsedRequestHeapFrameInstruction, type ParsedRequestUnitsInstruction, type ParsedSetComputeUnitLimitInstruction, type ParsedSetComputeUnitPriceInstruction, type ParsedSetLoadedAccountsDataSizeLimitInstruction } from '../instructions';
export declare const COMPUTE_BUDGET_PROGRAM_ADDRESS: Address<"ComputeBudget111111111111111111111111111111">;
export declare enum ComputeBudgetInstruction {
    RequestUnits = 0,
    RequestHeapFrame = 1,
    SetComputeUnitLimit = 2,
    SetComputeUnitPrice = 3,
    SetLoadedAccountsDataSizeLimit = 4
}
export declare function identifyComputeBudgetInstruction(instruction: {
    data: ReadonlyUint8Array;
} | ReadonlyUint8Array): ComputeBudgetInstruction;
export type ParsedComputeBudgetInstruction<TProgram extends string = 'ComputeBudget111111111111111111111111111111'> = ({
    instructionType: ComputeBudgetInstruction.RequestUnits;
} & ParsedRequestUnitsInstruction<TProgram>) | ({
    instructionType: ComputeBudgetInstruction.RequestHeapFrame;
} & ParsedRequestHeapFrameInstruction<TProgram>) | ({
    instructionType: ComputeBudgetInstruction.SetComputeUnitLimit;
} & ParsedSetComputeUnitLimitInstruction<TProgram>) | ({
    instructionType: ComputeBudgetInstruction.SetComputeUnitPrice;
} & ParsedSetComputeUnitPriceInstruction<TProgram>) | ({
    instructionType: ComputeBudgetInstruction.SetLoadedAccountsDataSizeLimit;
} & ParsedSetLoadedAccountsDataSizeLimitInstruction<TProgram>);
//# sourceMappingURL=computeBudget.d.ts.map