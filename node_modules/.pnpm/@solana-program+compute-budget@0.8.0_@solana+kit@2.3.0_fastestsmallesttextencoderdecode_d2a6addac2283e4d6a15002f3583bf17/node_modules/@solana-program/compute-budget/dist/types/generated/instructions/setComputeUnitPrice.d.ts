/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData } from '@solana/kit';
import { COMPUTE_BUDGET_PROGRAM_ADDRESS } from '../programs';
export declare const SET_COMPUTE_UNIT_PRICE_DISCRIMINATOR = 3;
export declare function getSetComputeUnitPriceDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type SetComputeUnitPriceInstruction<TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS, TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<TRemainingAccounts>;
export type SetComputeUnitPriceInstructionData = {
    discriminator: number;
    /** Transaction compute unit price used for prioritization fees. */
    microLamports: bigint;
};
export type SetComputeUnitPriceInstructionDataArgs = {
    /** Transaction compute unit price used for prioritization fees. */
    microLamports: number | bigint;
};
export declare function getSetComputeUnitPriceInstructionDataEncoder(): Encoder<SetComputeUnitPriceInstructionDataArgs>;
export declare function getSetComputeUnitPriceInstructionDataDecoder(): Decoder<SetComputeUnitPriceInstructionData>;
export declare function getSetComputeUnitPriceInstructionDataCodec(): Codec<SetComputeUnitPriceInstructionDataArgs, SetComputeUnitPriceInstructionData>;
export type SetComputeUnitPriceInput = {
    microLamports: SetComputeUnitPriceInstructionDataArgs['microLamports'];
};
export declare function getSetComputeUnitPriceInstruction<TProgramAddress extends Address = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS>(input: SetComputeUnitPriceInput, config?: {
    programAddress?: TProgramAddress;
}): SetComputeUnitPriceInstruction<TProgramAddress>;
export type ParsedSetComputeUnitPriceInstruction<TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS> = {
    programAddress: Address<TProgram>;
    data: SetComputeUnitPriceInstructionData;
};
export declare function parseSetComputeUnitPriceInstruction<TProgram extends string>(instruction: IInstruction<TProgram> & IInstructionWithData<Uint8Array>): ParsedSetComputeUnitPriceInstruction<TProgram>;
//# sourceMappingURL=setComputeUnitPrice.d.ts.map