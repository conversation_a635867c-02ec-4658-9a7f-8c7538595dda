/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData } from '@solana/kit';
import { COMPUTE_BUDGET_PROGRAM_ADDRESS } from '../programs';
export declare const REQUEST_HEAP_FRAME_DISCRIMINATOR = 1;
export declare function getRequestHeapFrameDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type RequestHeapFrameInstruction<TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS, TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<TRemainingAccounts>;
export type RequestHeapFrameInstructionData = {
    discriminator: number;
    /**
     * Requested transaction-wide program heap size in bytes.
     * Must be multiple of 1024. Applies to each program, including CPIs.
     */
    bytes: number;
};
export type RequestHeapFrameInstructionDataArgs = {
    /**
     * Requested transaction-wide program heap size in bytes.
     * Must be multiple of 1024. Applies to each program, including CPIs.
     */
    bytes: number;
};
export declare function getRequestHeapFrameInstructionDataEncoder(): Encoder<RequestHeapFrameInstructionDataArgs>;
export declare function getRequestHeapFrameInstructionDataDecoder(): Decoder<RequestHeapFrameInstructionData>;
export declare function getRequestHeapFrameInstructionDataCodec(): Codec<RequestHeapFrameInstructionDataArgs, RequestHeapFrameInstructionData>;
export type RequestHeapFrameInput = {
    bytes: RequestHeapFrameInstructionDataArgs['bytes'];
};
export declare function getRequestHeapFrameInstruction<TProgramAddress extends Address = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS>(input: RequestHeapFrameInput, config?: {
    programAddress?: TProgramAddress;
}): RequestHeapFrameInstruction<TProgramAddress>;
export type ParsedRequestHeapFrameInstruction<TProgram extends string = typeof COMPUTE_BUDGET_PROGRAM_ADDRESS> = {
    programAddress: Address<TProgram>;
    data: RequestHeapFrameInstructionData;
};
export declare function parseRequestHeapFrameInstruction<TProgram extends string>(instruction: IInstruction<TProgram> & IInstructionWithData<Uint8Array>): ParsedRequestHeapFrameInstruction<TProgram>;
//# sourceMappingURL=requestHeapFrame.d.ts.map