/**
 * A provisory compute unit limit is used to indicate that the transaction
 * should be estimated for compute units before being sent to the network.
 *
 * Setting it to zero ensures the transaction fails unless it is properly estimated.
 */
export declare const PROVISORY_COMPUTE_UNIT_LIMIT = 0;
/**
 * The maximum compute unit limit that can be set for a transaction.
 */
export declare const MAX_COMPUTE_UNIT_LIMIT = 1400000;
//# sourceMappingURL=constants.d.ts.map