/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData, type WritableAccount } from '@solana/kit';
import { SYSTEM_PROGRAM_ADDRESS } from '../programs';
export declare const UPGRADE_NONCE_ACCOUNT_DISCRIMINATOR = 12;
export declare function getUpgradeNonceAccountDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type UpgradeNonceAccountInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountNonceAccount extends string | IAccountMeta<string> = string, TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<[
    TAccountNonceAccount extends string ? WritableAccount<TAccountNonceAccount> : TAccountNonceAccount,
    ...TRemainingAccounts
]>;
export type UpgradeNonceAccountInstructionData = {
    discriminator: number;
};
export type UpgradeNonceAccountInstructionDataArgs = {};
export declare function getUpgradeNonceAccountInstructionDataEncoder(): Encoder<UpgradeNonceAccountInstructionDataArgs>;
export declare function getUpgradeNonceAccountInstructionDataDecoder(): Decoder<UpgradeNonceAccountInstructionData>;
export declare function getUpgradeNonceAccountInstructionDataCodec(): Codec<UpgradeNonceAccountInstructionDataArgs, UpgradeNonceAccountInstructionData>;
export type UpgradeNonceAccountInput<TAccountNonceAccount extends string = string> = {
    nonceAccount: Address<TAccountNonceAccount>;
};
export declare function getUpgradeNonceAccountInstruction<TAccountNonceAccount extends string, TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS>(input: UpgradeNonceAccountInput<TAccountNonceAccount>, config?: {
    programAddress?: TProgramAddress;
}): UpgradeNonceAccountInstruction<TProgramAddress, TAccountNonceAccount>;
export type ParsedUpgradeNonceAccountInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[]> = {
    programAddress: Address<TProgram>;
    accounts: {
        nonceAccount: TAccountMetas[0];
    };
    data: UpgradeNonceAccountInstructionData;
};
export declare function parseUpgradeNonceAccountInstruction<TProgram extends string, TAccountMetas extends readonly IAccountMeta[]>(instruction: IInstruction<TProgram> & IInstructionWithAccounts<TAccountMetas> & IInstructionWithData<Uint8Array>): ParsedUpgradeNonceAccountInstruction<TProgram, TAccountMetas>;
//# sourceMappingURL=upgradeNonceAccount.d.ts.map