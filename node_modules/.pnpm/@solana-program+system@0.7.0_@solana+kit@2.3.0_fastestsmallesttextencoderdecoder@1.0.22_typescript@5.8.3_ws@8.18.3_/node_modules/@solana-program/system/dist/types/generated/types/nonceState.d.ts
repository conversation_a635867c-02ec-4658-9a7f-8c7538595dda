/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Codec, type Decoder, type Encoder } from '@solana/kit';
export declare enum NonceState {
    Uninitialized = 0,
    Initialized = 1
}
export type NonceStateArgs = NonceState;
export declare function getNonceStateEncoder(): Encoder<NonceStateArgs>;
export declare function getNonceStateDecoder(): Decoder<NonceState>;
export declare function getNonceStateCodec(): Codec<NonceStateArgs, NonceState>;
//# sourceMappingURL=nonceState.d.ts.map