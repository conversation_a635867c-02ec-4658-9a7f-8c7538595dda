/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { fetchEncodedAccount, fetchEncodedAccounts, type Account, type Address, type Codec, type Decoder, type EncodedAccount, type Encoder, type FetchAccountConfig, type FetchAccountsConfig, type MaybeAccount, type MaybeEncodedAccount } from '@solana/kit';
import { type NonceState, type NonceStateArgs, type NonceVersion, type NonceVersionArgs } from '../types';
export type Nonce = {
    version: NonceVersion;
    state: NonceState;
    authority: Address;
    blockhash: Address;
    lamportsPerSignature: bigint;
};
export type NonceArgs = {
    version: NonceVersionArgs;
    state: NonceStateArgs;
    authority: Address;
    blockhash: Address;
    lamportsPerSignature: number | bigint;
};
export declare function getNonceEncoder(): Encoder<NonceArgs>;
export declare function getNonceDecoder(): Decoder<Nonce>;
export declare function getNonceCodec(): Codec<NonceArgs, Nonce>;
export declare function decodeNonce<TAddress extends string = string>(encodedAccount: EncodedAccount<TAddress>): Account<Nonce, TAddress>;
export declare function decodeNonce<TAddress extends string = string>(encodedAccount: MaybeEncodedAccount<TAddress>): MaybeAccount<Nonce, TAddress>;
export declare function fetchNonce<TAddress extends string = string>(rpc: Parameters<typeof fetchEncodedAccount>[0], address: Address<TAddress>, config?: FetchAccountConfig): Promise<Account<Nonce, TAddress>>;
export declare function fetchMaybeNonce<TAddress extends string = string>(rpc: Parameters<typeof fetchEncodedAccount>[0], address: Address<TAddress>, config?: FetchAccountConfig): Promise<MaybeAccount<Nonce, TAddress>>;
export declare function fetchAllNonce(rpc: Parameters<typeof fetchEncodedAccounts>[0], addresses: Array<Address>, config?: FetchAccountsConfig): Promise<Account<Nonce>[]>;
export declare function fetchAllMaybeNonce(rpc: Parameters<typeof fetchEncodedAccounts>[0], addresses: Array<Address>, config?: FetchAccountsConfig): Promise<MaybeAccount<Nonce>[]>;
export declare function getNonceSize(): number;
//# sourceMappingURL=nonce.d.ts.map