/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData, type ReadonlyAccount, type WritableAccount } from '@solana/kit';
import { SYSTEM_PROGRAM_ADDRESS } from '../programs';
export declare const INITIALIZE_NONCE_ACCOUNT_DISCRIMINATOR = 6;
export declare function getInitializeNonceAccountDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type InitializeNonceAccountInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountNonceAccount extends string | IAccountMeta<string> = string, TAccountRecentBlockhashesSysvar extends string | IAccountMeta<string> = 'SysvarRecentB1ockHashes11111111111111111111', TAccountRentSysvar extends string | IAccountMeta<string> = 'SysvarRent111111111111111111111111111111111', TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<[
    TAccountNonceAccount extends string ? WritableAccount<TAccountNonceAccount> : TAccountNonceAccount,
    TAccountRecentBlockhashesSysvar extends string ? ReadonlyAccount<TAccountRecentBlockhashesSysvar> : TAccountRecentBlockhashesSysvar,
    TAccountRentSysvar extends string ? ReadonlyAccount<TAccountRentSysvar> : TAccountRentSysvar,
    ...TRemainingAccounts
]>;
export type InitializeNonceAccountInstructionData = {
    discriminator: number;
    nonceAuthority: Address;
};
export type InitializeNonceAccountInstructionDataArgs = {
    nonceAuthority: Address;
};
export declare function getInitializeNonceAccountInstructionDataEncoder(): Encoder<InitializeNonceAccountInstructionDataArgs>;
export declare function getInitializeNonceAccountInstructionDataDecoder(): Decoder<InitializeNonceAccountInstructionData>;
export declare function getInitializeNonceAccountInstructionDataCodec(): Codec<InitializeNonceAccountInstructionDataArgs, InitializeNonceAccountInstructionData>;
export type InitializeNonceAccountInput<TAccountNonceAccount extends string = string, TAccountRecentBlockhashesSysvar extends string = string, TAccountRentSysvar extends string = string> = {
    nonceAccount: Address<TAccountNonceAccount>;
    recentBlockhashesSysvar?: Address<TAccountRecentBlockhashesSysvar>;
    rentSysvar?: Address<TAccountRentSysvar>;
    nonceAuthority: InitializeNonceAccountInstructionDataArgs['nonceAuthority'];
};
export declare function getInitializeNonceAccountInstruction<TAccountNonceAccount extends string, TAccountRecentBlockhashesSysvar extends string, TAccountRentSysvar extends string, TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS>(input: InitializeNonceAccountInput<TAccountNonceAccount, TAccountRecentBlockhashesSysvar, TAccountRentSysvar>, config?: {
    programAddress?: TProgramAddress;
}): InitializeNonceAccountInstruction<TProgramAddress, TAccountNonceAccount, TAccountRecentBlockhashesSysvar, TAccountRentSysvar>;
export type ParsedInitializeNonceAccountInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[]> = {
    programAddress: Address<TProgram>;
    accounts: {
        nonceAccount: TAccountMetas[0];
        recentBlockhashesSysvar: TAccountMetas[1];
        rentSysvar: TAccountMetas[2];
    };
    data: InitializeNonceAccountInstructionData;
};
export declare function parseInitializeNonceAccountInstruction<TProgram extends string, TAccountMetas extends readonly IAccountMeta[]>(instruction: IInstruction<TProgram> & IInstructionWithAccounts<TAccountMetas> & IInstructionWithData<Uint8Array>): ParsedInitializeNonceAccountInstruction<TProgram, TAccountMetas>;
//# sourceMappingURL=initializeNonceAccount.d.ts.map