/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM, type SolanaError } from '@solana/kit';
/** AccountAlreadyInUse: an account with the same address already exists */
export declare const SYSTEM_ERROR__ACCOUNT_ALREADY_IN_USE = 0;
/** ResultWithNegativeLamports: account does not have enough SOL to perform the operation */
export declare const SYSTEM_ERROR__RESULT_WITH_NEGATIVE_LAMPORTS = 1;
/** InvalidProgramId: cannot assign account to this program id */
export declare const SYSTEM_ERROR__INVALID_PROGRAM_ID = 2;
/** InvalidAccountDataLength: cannot allocate account data of this length */
export declare const SYSTEM_ERROR__INVALID_ACCOUNT_DATA_LENGTH = 3;
/** MaxSeedLengthExceeded: length of requested seed is too long */
export declare const SYSTEM_ERROR__MAX_SEED_LENGTH_EXCEEDED = 4;
/** AddressWithSeedMismatch: provided address does not match addressed derived from seed */
export declare const SYSTEM_ERROR__ADDRESS_WITH_SEED_MISMATCH = 5;
/** NonceNoRecentBlockhashes: advancing stored nonce requires a populated RecentBlockhashes sysvar */
export declare const SYSTEM_ERROR__NONCE_NO_RECENT_BLOCKHASHES = 6;
/** NonceBlockhashNotExpired: stored nonce is still in recent_blockhashes */
export declare const SYSTEM_ERROR__NONCE_BLOCKHASH_NOT_EXPIRED = 7;
/** NonceUnexpectedBlockhashValue: specified nonce does not match stored nonce */
export declare const SYSTEM_ERROR__NONCE_UNEXPECTED_BLOCKHASH_VALUE = 8;
export type SystemError = typeof SYSTEM_ERROR__ACCOUNT_ALREADY_IN_USE | typeof SYSTEM_ERROR__ADDRESS_WITH_SEED_MISMATCH | typeof SYSTEM_ERROR__INVALID_ACCOUNT_DATA_LENGTH | typeof SYSTEM_ERROR__INVALID_PROGRAM_ID | typeof SYSTEM_ERROR__MAX_SEED_LENGTH_EXCEEDED | typeof SYSTEM_ERROR__NONCE_BLOCKHASH_NOT_EXPIRED | typeof SYSTEM_ERROR__NONCE_NO_RECENT_BLOCKHASHES | typeof SYSTEM_ERROR__NONCE_UNEXPECTED_BLOCKHASH_VALUE | typeof SYSTEM_ERROR__RESULT_WITH_NEGATIVE_LAMPORTS;
export declare function getSystemErrorMessage(code: SystemError): string;
export declare function isSystemError<TProgramErrorCode extends SystemError>(error: unknown, transactionMessage: {
    instructions: Record<number, {
        programAddress: Address;
    }>;
}, code?: TProgramErrorCode): error is SolanaError<typeof SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM> & Readonly<{
    context: Readonly<{
        code: TProgramErrorCode;
    }>;
}>;
//# sourceMappingURL=system.d.ts.map