/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IAccountSignerMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData, type ReadonlyAccount, type ReadonlySignerAccount, type TransactionSigner, type WritableAccount } from '@solana/kit';
import { SYSTEM_PROGRAM_ADDRESS } from '../programs';
export declare const WITHDRAW_NONCE_ACCOUNT_DISCRIMINATOR = 5;
export declare function getWithdrawNonceAccountDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type WithdrawNonceAccountInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountNonceAccount extends string | IAccountMeta<string> = string, TAccountRecipientAccount extends string | IAccountMeta<string> = string, TAccountRecentBlockhashesSysvar extends string | IAccountMeta<string> = 'SysvarRecentB1ockHashes11111111111111111111', TAccountRentSysvar extends string | IAccountMeta<string> = 'SysvarRent111111111111111111111111111111111', TAccountNonceAuthority extends string | IAccountMeta<string> = string, TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<[
    TAccountNonceAccount extends string ? WritableAccount<TAccountNonceAccount> : TAccountNonceAccount,
    TAccountRecipientAccount extends string ? WritableAccount<TAccountRecipientAccount> : TAccountRecipientAccount,
    TAccountRecentBlockhashesSysvar extends string ? ReadonlyAccount<TAccountRecentBlockhashesSysvar> : TAccountRecentBlockhashesSysvar,
    TAccountRentSysvar extends string ? ReadonlyAccount<TAccountRentSysvar> : TAccountRentSysvar,
    TAccountNonceAuthority extends string ? ReadonlySignerAccount<TAccountNonceAuthority> & IAccountSignerMeta<TAccountNonceAuthority> : TAccountNonceAuthority,
    ...TRemainingAccounts
]>;
export type WithdrawNonceAccountInstructionData = {
    discriminator: number;
    withdrawAmount: bigint;
};
export type WithdrawNonceAccountInstructionDataArgs = {
    withdrawAmount: number | bigint;
};
export declare function getWithdrawNonceAccountInstructionDataEncoder(): Encoder<WithdrawNonceAccountInstructionDataArgs>;
export declare function getWithdrawNonceAccountInstructionDataDecoder(): Decoder<WithdrawNonceAccountInstructionData>;
export declare function getWithdrawNonceAccountInstructionDataCodec(): Codec<WithdrawNonceAccountInstructionDataArgs, WithdrawNonceAccountInstructionData>;
export type WithdrawNonceAccountInput<TAccountNonceAccount extends string = string, TAccountRecipientAccount extends string = string, TAccountRecentBlockhashesSysvar extends string = string, TAccountRentSysvar extends string = string, TAccountNonceAuthority extends string = string> = {
    nonceAccount: Address<TAccountNonceAccount>;
    recipientAccount: Address<TAccountRecipientAccount>;
    recentBlockhashesSysvar?: Address<TAccountRecentBlockhashesSysvar>;
    rentSysvar?: Address<TAccountRentSysvar>;
    nonceAuthority: TransactionSigner<TAccountNonceAuthority>;
    withdrawAmount: WithdrawNonceAccountInstructionDataArgs['withdrawAmount'];
};
export declare function getWithdrawNonceAccountInstruction<TAccountNonceAccount extends string, TAccountRecipientAccount extends string, TAccountRecentBlockhashesSysvar extends string, TAccountRentSysvar extends string, TAccountNonceAuthority extends string, TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS>(input: WithdrawNonceAccountInput<TAccountNonceAccount, TAccountRecipientAccount, TAccountRecentBlockhashesSysvar, TAccountRentSysvar, TAccountNonceAuthority>, config?: {
    programAddress?: TProgramAddress;
}): WithdrawNonceAccountInstruction<TProgramAddress, TAccountNonceAccount, TAccountRecipientAccount, TAccountRecentBlockhashesSysvar, TAccountRentSysvar, TAccountNonceAuthority>;
export type ParsedWithdrawNonceAccountInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[]> = {
    programAddress: Address<TProgram>;
    accounts: {
        nonceAccount: TAccountMetas[0];
        recipientAccount: TAccountMetas[1];
        recentBlockhashesSysvar: TAccountMetas[2];
        rentSysvar: TAccountMetas[3];
        nonceAuthority: TAccountMetas[4];
    };
    data: WithdrawNonceAccountInstructionData;
};
export declare function parseWithdrawNonceAccountInstruction<TProgram extends string, TAccountMetas extends readonly IAccountMeta[]>(instruction: IInstruction<TProgram> & IInstructionWithAccounts<TAccountMetas> & IInstructionWithData<Uint8Array>): ParsedWithdrawNonceAccountInstruction<TProgram, TAccountMetas>;
//# sourceMappingURL=withdrawNonceAccount.d.ts.map