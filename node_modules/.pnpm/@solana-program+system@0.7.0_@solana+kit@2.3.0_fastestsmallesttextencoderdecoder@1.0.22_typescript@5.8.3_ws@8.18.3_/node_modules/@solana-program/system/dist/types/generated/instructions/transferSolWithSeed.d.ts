/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IAccountSignerMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData, type ReadonlySignerAccount, type TransactionSigner, type WritableAccount } from '@solana/kit';
import { SYSTEM_PROGRAM_ADDRESS } from '../programs';
export declare const TRANSFER_SOL_WITH_SEED_DISCRIMINATOR = 11;
export declare function getTransferSolWithSeedDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type TransferSolWithSeedInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountSource extends string | IAccountMeta<string> = string, TAccountBaseAccount extends string | IAccountMeta<string> = string, TAccountDestination extends string | IAccountMeta<string> = string, TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<[
    TAccountSource extends string ? WritableAccount<TAccountSource> : TAccountSource,
    TAccountBaseAccount extends string ? ReadonlySignerAccount<TAccountBaseAccount> & IAccountSignerMeta<TAccountBaseAccount> : TAccountBaseAccount,
    TAccountDestination extends string ? WritableAccount<TAccountDestination> : TAccountDestination,
    ...TRemainingAccounts
]>;
export type TransferSolWithSeedInstructionData = {
    discriminator: number;
    amount: bigint;
    fromSeed: string;
    fromOwner: Address;
};
export type TransferSolWithSeedInstructionDataArgs = {
    amount: number | bigint;
    fromSeed: string;
    fromOwner: Address;
};
export declare function getTransferSolWithSeedInstructionDataEncoder(): Encoder<TransferSolWithSeedInstructionDataArgs>;
export declare function getTransferSolWithSeedInstructionDataDecoder(): Decoder<TransferSolWithSeedInstructionData>;
export declare function getTransferSolWithSeedInstructionDataCodec(): Codec<TransferSolWithSeedInstructionDataArgs, TransferSolWithSeedInstructionData>;
export type TransferSolWithSeedInput<TAccountSource extends string = string, TAccountBaseAccount extends string = string, TAccountDestination extends string = string> = {
    source: Address<TAccountSource>;
    baseAccount: TransactionSigner<TAccountBaseAccount>;
    destination: Address<TAccountDestination>;
    amount: TransferSolWithSeedInstructionDataArgs['amount'];
    fromSeed: TransferSolWithSeedInstructionDataArgs['fromSeed'];
    fromOwner: TransferSolWithSeedInstructionDataArgs['fromOwner'];
};
export declare function getTransferSolWithSeedInstruction<TAccountSource extends string, TAccountBaseAccount extends string, TAccountDestination extends string, TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS>(input: TransferSolWithSeedInput<TAccountSource, TAccountBaseAccount, TAccountDestination>, config?: {
    programAddress?: TProgramAddress;
}): TransferSolWithSeedInstruction<TProgramAddress, TAccountSource, TAccountBaseAccount, TAccountDestination>;
export type ParsedTransferSolWithSeedInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[]> = {
    programAddress: Address<TProgram>;
    accounts: {
        source: TAccountMetas[0];
        baseAccount: TAccountMetas[1];
        destination: TAccountMetas[2];
    };
    data: TransferSolWithSeedInstructionData;
};
export declare function parseTransferSolWithSeedInstruction<TProgram extends string, TAccountMetas extends readonly IAccountMeta[]>(instruction: IInstruction<TProgram> & IInstructionWithAccounts<TAccountMetas> & IInstructionWithData<Uint8Array>): ParsedTransferSolWithSeedInstruction<TProgram, TAccountMetas>;
//# sourceMappingURL=transferSolWithSeed.d.ts.map