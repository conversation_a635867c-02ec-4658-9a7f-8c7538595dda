/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Codec, type Decoder, type Encoder } from '@solana/kit';
export declare enum NonceVersion {
    Legacy = 0,
    Current = 1
}
export type NonceVersionArgs = NonceVersion;
export declare function getNonceVersionEncoder(): Encoder<NonceVersionArgs>;
export declare function getNonceVersionDecoder(): Decoder<NonceVersion>;
export declare function getNonceVersionCodec(): Codec<NonceVersionArgs, NonceVersion>;
//# sourceMappingURL=nonceVersion.d.ts.map