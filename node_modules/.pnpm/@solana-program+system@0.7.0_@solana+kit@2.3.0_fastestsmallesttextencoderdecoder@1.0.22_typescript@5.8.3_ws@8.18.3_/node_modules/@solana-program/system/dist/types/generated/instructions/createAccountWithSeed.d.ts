/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IAccountSignerMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData, type ReadonlySignerAccount, type TransactionSigner, type WritableAccount, type WritableSignerAccount } from '@solana/kit';
import { SYSTEM_PROGRAM_ADDRESS } from '../programs';
export declare const CREATE_ACCOUNT_WITH_SEED_DISCRIMINATOR = 3;
export declare function getCreateAccountWithSeedDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type CreateAccountWithSeedInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountPayer extends string | IAccountMeta<string> = string, TAccountNewAccount extends string | IAccountMeta<string> = string, TAccountBaseAccount extends string | IAccountMeta<string> = string, TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<[
    TAccountPayer extends string ? WritableSignerAccount<TAccountPayer> & IAccountSignerMeta<TAccountPayer> : TAccountPayer,
    TAccountNewAccount extends string ? WritableAccount<TAccountNewAccount> : TAccountNewAccount,
    TAccountBaseAccount extends string ? ReadonlySignerAccount<TAccountBaseAccount> & IAccountSignerMeta<TAccountBaseAccount> : TAccountBaseAccount,
    ...TRemainingAccounts
]>;
export type CreateAccountWithSeedInstructionData = {
    discriminator: number;
    base: Address;
    seed: string;
    amount: bigint;
    space: bigint;
    programAddress: Address;
};
export type CreateAccountWithSeedInstructionDataArgs = {
    base: Address;
    seed: string;
    amount: number | bigint;
    space: number | bigint;
    programAddress: Address;
};
export declare function getCreateAccountWithSeedInstructionDataEncoder(): Encoder<CreateAccountWithSeedInstructionDataArgs>;
export declare function getCreateAccountWithSeedInstructionDataDecoder(): Decoder<CreateAccountWithSeedInstructionData>;
export declare function getCreateAccountWithSeedInstructionDataCodec(): Codec<CreateAccountWithSeedInstructionDataArgs, CreateAccountWithSeedInstructionData>;
export type CreateAccountWithSeedInput<TAccountPayer extends string = string, TAccountNewAccount extends string = string, TAccountBaseAccount extends string = string> = {
    payer: TransactionSigner<TAccountPayer>;
    newAccount: Address<TAccountNewAccount>;
    baseAccount: TransactionSigner<TAccountBaseAccount>;
    base: CreateAccountWithSeedInstructionDataArgs['base'];
    seed: CreateAccountWithSeedInstructionDataArgs['seed'];
    amount: CreateAccountWithSeedInstructionDataArgs['amount'];
    space: CreateAccountWithSeedInstructionDataArgs['space'];
    programAddress: CreateAccountWithSeedInstructionDataArgs['programAddress'];
};
export declare function getCreateAccountWithSeedInstruction<TAccountPayer extends string, TAccountNewAccount extends string, TAccountBaseAccount extends string, TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS>(input: CreateAccountWithSeedInput<TAccountPayer, TAccountNewAccount, TAccountBaseAccount>, config?: {
    programAddress?: TProgramAddress;
}): CreateAccountWithSeedInstruction<TProgramAddress, TAccountPayer, TAccountNewAccount, TAccountBaseAccount>;
export type ParsedCreateAccountWithSeedInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[]> = {
    programAddress: Address<TProgram>;
    accounts: {
        payer: TAccountMetas[0];
        newAccount: TAccountMetas[1];
        baseAccount: TAccountMetas[2];
    };
    data: CreateAccountWithSeedInstructionData;
};
export declare function parseCreateAccountWithSeedInstruction<TProgram extends string, TAccountMetas extends readonly IAccountMeta[]>(instruction: IInstruction<TProgram> & IInstructionWithAccounts<TAccountMetas> & IInstructionWithData<Uint8Array>): ParsedCreateAccountWithSeedInstruction<TProgram, TAccountMetas>;
//# sourceMappingURL=createAccountWithSeed.d.ts.map