/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IAccountSignerMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData, type ReadonlySignerAccount, type TransactionSigner, type WritableAccount } from '@solana/kit';
import { SYSTEM_PROGRAM_ADDRESS } from '../programs';
export declare const ALLOCATE_WITH_SEED_DISCRIMINATOR = 9;
export declare function getAllocateWithSeedDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type AllocateWithSeedInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountNewAccount extends string | IAccountMeta<string> = string, TAccountBaseAccount extends string | IAccountMeta<string> = string, TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<[
    TAccountNewAccount extends string ? WritableAccount<TAccountNewAccount> : TAccountNewAccount,
    TAccountBaseAccount extends string ? ReadonlySignerAccount<TAccountBaseAccount> & IAccountSignerMeta<TAccountBaseAccount> : TAccountBaseAccount,
    ...TRemainingAccounts
]>;
export type AllocateWithSeedInstructionData = {
    discriminator: number;
    base: Address;
    seed: string;
    space: bigint;
    programAddress: Address;
};
export type AllocateWithSeedInstructionDataArgs = {
    base: Address;
    seed: string;
    space: number | bigint;
    programAddress: Address;
};
export declare function getAllocateWithSeedInstructionDataEncoder(): Encoder<AllocateWithSeedInstructionDataArgs>;
export declare function getAllocateWithSeedInstructionDataDecoder(): Decoder<AllocateWithSeedInstructionData>;
export declare function getAllocateWithSeedInstructionDataCodec(): Codec<AllocateWithSeedInstructionDataArgs, AllocateWithSeedInstructionData>;
export type AllocateWithSeedInput<TAccountNewAccount extends string = string, TAccountBaseAccount extends string = string> = {
    newAccount: Address<TAccountNewAccount>;
    baseAccount: TransactionSigner<TAccountBaseAccount>;
    base: AllocateWithSeedInstructionDataArgs['base'];
    seed: AllocateWithSeedInstructionDataArgs['seed'];
    space: AllocateWithSeedInstructionDataArgs['space'];
    programAddress: AllocateWithSeedInstructionDataArgs['programAddress'];
};
export declare function getAllocateWithSeedInstruction<TAccountNewAccount extends string, TAccountBaseAccount extends string, TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS>(input: AllocateWithSeedInput<TAccountNewAccount, TAccountBaseAccount>, config?: {
    programAddress?: TProgramAddress;
}): AllocateWithSeedInstruction<TProgramAddress, TAccountNewAccount, TAccountBaseAccount>;
export type ParsedAllocateWithSeedInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[]> = {
    programAddress: Address<TProgram>;
    accounts: {
        newAccount: TAccountMetas[0];
        baseAccount: TAccountMetas[1];
    };
    data: AllocateWithSeedInstructionData;
};
export declare function parseAllocateWithSeedInstruction<TProgram extends string, TAccountMetas extends readonly IAccountMeta[]>(instruction: IInstruction<TProgram> & IInstructionWithAccounts<TAccountMetas> & IInstructionWithData<Uint8Array>): ParsedAllocateWithSeedInstruction<TProgram, TAccountMetas>;
//# sourceMappingURL=allocateWithSeed.d.ts.map