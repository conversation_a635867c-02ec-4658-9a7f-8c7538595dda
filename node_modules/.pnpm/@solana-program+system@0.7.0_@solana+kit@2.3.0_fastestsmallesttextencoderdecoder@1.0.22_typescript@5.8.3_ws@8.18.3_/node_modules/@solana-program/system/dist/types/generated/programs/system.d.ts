/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type ReadonlyUint8Array } from '@solana/kit';
import { type ParsedAdvanceNonceAccountInstruction, type ParsedAllocateInstruction, type ParsedAllocateWithSeedInstruction, type ParsedAssignInstruction, type ParsedAssignWithSeedInstruction, type ParsedAuthorizeNonceAccountInstruction, type ParsedCreateAccountInstruction, type ParsedCreateAccountWithSeedInstruction, type ParsedInitializeNonceAccountInstruction, type ParsedTransferSolInstruction, type ParsedTransferSolWithSeedInstruction, type ParsedUpgradeNonceAccountInstruction, type ParsedWithdrawNonceAccountInstruction } from '../instructions';
export declare const SYSTEM_PROGRAM_ADDRESS: Address<"11111111111111111111111111111111">;
export declare enum SystemAccount {
    Nonce = 0
}
export declare enum SystemInstruction {
    CreateAccount = 0,
    Assign = 1,
    TransferSol = 2,
    CreateAccountWithSeed = 3,
    AdvanceNonceAccount = 4,
    WithdrawNonceAccount = 5,
    InitializeNonceAccount = 6,
    AuthorizeNonceAccount = 7,
    Allocate = 8,
    AllocateWithSeed = 9,
    AssignWithSeed = 10,
    TransferSolWithSeed = 11,
    UpgradeNonceAccount = 12
}
export declare function identifySystemInstruction(instruction: {
    data: ReadonlyUint8Array;
} | ReadonlyUint8Array): SystemInstruction;
export type ParsedSystemInstruction<TProgram extends string = '11111111111111111111111111111111'> = ({
    instructionType: SystemInstruction.CreateAccount;
} & ParsedCreateAccountInstruction<TProgram>) | ({
    instructionType: SystemInstruction.Assign;
} & ParsedAssignInstruction<TProgram>) | ({
    instructionType: SystemInstruction.TransferSol;
} & ParsedTransferSolInstruction<TProgram>) | ({
    instructionType: SystemInstruction.CreateAccountWithSeed;
} & ParsedCreateAccountWithSeedInstruction<TProgram>) | ({
    instructionType: SystemInstruction.AdvanceNonceAccount;
} & ParsedAdvanceNonceAccountInstruction<TProgram>) | ({
    instructionType: SystemInstruction.WithdrawNonceAccount;
} & ParsedWithdrawNonceAccountInstruction<TProgram>) | ({
    instructionType: SystemInstruction.InitializeNonceAccount;
} & ParsedInitializeNonceAccountInstruction<TProgram>) | ({
    instructionType: SystemInstruction.AuthorizeNonceAccount;
} & ParsedAuthorizeNonceAccountInstruction<TProgram>) | ({
    instructionType: SystemInstruction.Allocate;
} & ParsedAllocateInstruction<TProgram>) | ({
    instructionType: SystemInstruction.AllocateWithSeed;
} & ParsedAllocateWithSeedInstruction<TProgram>) | ({
    instructionType: SystemInstruction.AssignWithSeed;
} & ParsedAssignWithSeedInstruction<TProgram>) | ({
    instructionType: SystemInstruction.TransferSolWithSeed;
} & ParsedTransferSolWithSeedInstruction<TProgram>) | ({
    instructionType: SystemInstruction.UpgradeNonceAccount;
} & ParsedUpgradeNonceAccountInstruction<TProgram>);
//# sourceMappingURL=system.d.ts.map