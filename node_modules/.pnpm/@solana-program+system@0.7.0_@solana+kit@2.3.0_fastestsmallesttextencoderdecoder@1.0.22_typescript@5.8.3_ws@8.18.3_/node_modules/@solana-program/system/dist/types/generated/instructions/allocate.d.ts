/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IAccountSignerMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData, type TransactionSigner, type WritableSignerAccount } from '@solana/kit';
import { SYSTEM_PROGRAM_ADDRESS } from '../programs';
export declare const ALLOCATE_DISCRIMINATOR = 8;
export declare function getAllocateDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type AllocateInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountNewAccount extends string | IAccountMeta<string> = string, TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<[
    TAccountNewAccount extends string ? WritableSignerAccount<TAccountNewAccount> & IAccountSignerMeta<TAccountNewAccount> : TAccountNewAccount,
    ...TRemainingAccounts
]>;
export type AllocateInstructionData = {
    discriminator: number;
    space: bigint;
};
export type AllocateInstructionDataArgs = {
    space: number | bigint;
};
export declare function getAllocateInstructionDataEncoder(): Encoder<AllocateInstructionDataArgs>;
export declare function getAllocateInstructionDataDecoder(): Decoder<AllocateInstructionData>;
export declare function getAllocateInstructionDataCodec(): Codec<AllocateInstructionDataArgs, AllocateInstructionData>;
export type AllocateInput<TAccountNewAccount extends string = string> = {
    newAccount: TransactionSigner<TAccountNewAccount>;
    space: AllocateInstructionDataArgs['space'];
};
export declare function getAllocateInstruction<TAccountNewAccount extends string, TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS>(input: AllocateInput<TAccountNewAccount>, config?: {
    programAddress?: TProgramAddress;
}): AllocateInstruction<TProgramAddress, TAccountNewAccount>;
export type ParsedAllocateInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[]> = {
    programAddress: Address<TProgram>;
    accounts: {
        newAccount: TAccountMetas[0];
    };
    data: AllocateInstructionData;
};
export declare function parseAllocateInstruction<TProgram extends string, TAccountMetas extends readonly IAccountMeta[]>(instruction: IInstruction<TProgram> & IInstructionWithAccounts<TAccountMetas> & IInstructionWithData<Uint8Array>): ParsedAllocateInstruction<TProgram, TAccountMetas>;
//# sourceMappingURL=allocate.d.ts.map