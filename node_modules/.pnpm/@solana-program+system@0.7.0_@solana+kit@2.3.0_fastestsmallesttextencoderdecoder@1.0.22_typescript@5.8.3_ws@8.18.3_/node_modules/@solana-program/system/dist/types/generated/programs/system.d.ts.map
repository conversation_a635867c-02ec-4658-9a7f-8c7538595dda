{"version": 3, "file": "system.d.ts", "sourceRoot": "", "sources": ["../../../../src/generated/programs/system.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,EAGL,KAAK,OAAO,EACZ,KAAK,kBAAkB,EACxB,MAAM,aAAa,CAAC;AACrB,OAAO,EACL,KAAK,oCAAoC,EACzC,KAAK,yBAAyB,EAC9B,KAAK,iCAAiC,EACtC,KAAK,uBAAuB,EAC5B,KAAK,+BAA+B,EACpC,KAAK,sCAAsC,EAC3C,KAAK,8BAA8B,EACnC,KAAK,sCAAsC,EAC3C,KAAK,uCAAuC,EAC5C,KAAK,4BAA4B,EACjC,KAAK,oCAAoC,EACzC,KAAK,oCAAoC,EACzC,KAAK,qCAAqC,EAC3C,MAAM,iBAAiB,CAAC;AAEzB,eAAO,MAAM,sBAAsB,EACK,OAAO,CAAC,kCAAkC,CAAC,CAAC;AAEpF,oBAAY,aAAa;IACvB,KAAK,IAAA;CACN;AAED,oBAAY,iBAAiB;IAC3B,aAAa,IAAA;IACb,MAAM,IAAA;IACN,WAAW,IAAA;IACX,qBAAqB,IAAA;IACrB,mBAAmB,IAAA;IACnB,oBAAoB,IAAA;IACpB,sBAAsB,IAAA;IACtB,qBAAqB,IAAA;IACrB,QAAQ,IAAA;IACR,gBAAgB,IAAA;IAChB,cAAc,KAAA;IACd,mBAAmB,KAAA;IACnB,mBAAmB,KAAA;CACpB;AAED,wBAAgB,yBAAyB,CACvC,WAAW,EAAE;IAAE,IAAI,EAAE,kBAAkB,CAAA;CAAE,GAAG,kBAAkB,GAC7D,iBAAiB,CA4CnB;AAED,MAAM,MAAM,uBAAuB,CACjC,QAAQ,SAAS,MAAM,GAAG,kCAAkC,IAE1D,CAAC;IACC,eAAe,EAAE,iBAAiB,CAAC,aAAa,CAAC;CAClD,GAAG,8BAA8B,CAAC,QAAQ,CAAC,CAAC,GAC7C,CAAC;IACC,eAAe,EAAE,iBAAiB,CAAC,MAAM,CAAC;CAC3C,GAAG,uBAAuB,CAAC,QAAQ,CAAC,CAAC,GACtC,CAAC;IACC,eAAe,EAAE,iBAAiB,CAAC,WAAW,CAAC;CAChD,GAAG,4BAA4B,CAAC,QAAQ,CAAC,CAAC,GAC3C,CAAC;IACC,eAAe,EAAE,iBAAiB,CAAC,qBAAqB,CAAC;CAC1D,GAAG,sCAAsC,CAAC,QAAQ,CAAC,CAAC,GACrD,CAAC;IACC,eAAe,EAAE,iBAAiB,CAAC,mBAAmB,CAAC;CACxD,GAAG,oCAAoC,CAAC,QAAQ,CAAC,CAAC,GACnD,CAAC;IACC,eAAe,EAAE,iBAAiB,CAAC,oBAAoB,CAAC;CACzD,GAAG,qCAAqC,CAAC,QAAQ,CAAC,CAAC,GACpD,CAAC;IACC,eAAe,EAAE,iBAAiB,CAAC,sBAAsB,CAAC;CAC3D,GAAG,uCAAuC,CAAC,QAAQ,CAAC,CAAC,GACtD,CAAC;IACC,eAAe,EAAE,iBAAiB,CAAC,qBAAqB,CAAC;CAC1D,GAAG,sCAAsC,CAAC,QAAQ,CAAC,CAAC,GACrD,CAAC;IACC,eAAe,EAAE,iBAAiB,CAAC,QAAQ,CAAC;CAC7C,GAAG,yBAAyB,CAAC,QAAQ,CAAC,CAAC,GACxC,CAAC;IACC,eAAe,EAAE,iBAAiB,CAAC,gBAAgB,CAAC;CACrD,GAAG,iCAAiC,CAAC,QAAQ,CAAC,CAAC,GAChD,CAAC;IACC,eAAe,EAAE,iBAAiB,CAAC,cAAc,CAAC;CACnD,GAAG,+BAA+B,CAAC,QAAQ,CAAC,CAAC,GAC9C,CAAC;IACC,eAAe,EAAE,iBAAiB,CAAC,mBAAmB,CAAC;CACxD,GAAG,oCAAoC,CAAC,QAAQ,CAAC,CAAC,GACnD,CAAC;IACC,eAAe,EAAE,iBAAiB,CAAC,mBAAmB,CAAC;CACxD,GAAG,oCAAoC,CAAC,QAAQ,CAAC,CAAC,CAAC"}