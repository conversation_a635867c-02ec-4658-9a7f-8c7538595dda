/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IAccountSignerMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData, type ReadonlySignerAccount, type TransactionSigner, type WritableAccount } from '@solana/kit';
import { SYSTEM_PROGRAM_ADDRESS } from '../programs';
export declare const ASSIGN_WITH_SEED_DISCRIMINATOR = 10;
export declare function getAssignWithSeedDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type AssignWithSeedInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountAccount extends string | IAccountMeta<string> = string, TAccountBaseAccount extends string | IAccountMeta<string> = string, TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<[
    TAccountAccount extends string ? WritableAccount<TAccountAccount> : TAccountAccount,
    TAccountBaseAccount extends string ? ReadonlySignerAccount<TAccountBaseAccount> & IAccountSignerMeta<TAccountBaseAccount> : TAccountBaseAccount,
    ...TRemainingAccounts
]>;
export type AssignWithSeedInstructionData = {
    discriminator: number;
    base: Address;
    seed: string;
    programAddress: Address;
};
export type AssignWithSeedInstructionDataArgs = {
    base: Address;
    seed: string;
    programAddress: Address;
};
export declare function getAssignWithSeedInstructionDataEncoder(): Encoder<AssignWithSeedInstructionDataArgs>;
export declare function getAssignWithSeedInstructionDataDecoder(): Decoder<AssignWithSeedInstructionData>;
export declare function getAssignWithSeedInstructionDataCodec(): Codec<AssignWithSeedInstructionDataArgs, AssignWithSeedInstructionData>;
export type AssignWithSeedInput<TAccountAccount extends string = string, TAccountBaseAccount extends string = string> = {
    account: Address<TAccountAccount>;
    baseAccount: TransactionSigner<TAccountBaseAccount>;
    base: AssignWithSeedInstructionDataArgs['base'];
    seed: AssignWithSeedInstructionDataArgs['seed'];
    programAddress: AssignWithSeedInstructionDataArgs['programAddress'];
};
export declare function getAssignWithSeedInstruction<TAccountAccount extends string, TAccountBaseAccount extends string, TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS>(input: AssignWithSeedInput<TAccountAccount, TAccountBaseAccount>, config?: {
    programAddress?: TProgramAddress;
}): AssignWithSeedInstruction<TProgramAddress, TAccountAccount, TAccountBaseAccount>;
export type ParsedAssignWithSeedInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[]> = {
    programAddress: Address<TProgram>;
    accounts: {
        account: TAccountMetas[0];
        baseAccount: TAccountMetas[1];
    };
    data: AssignWithSeedInstructionData;
};
export declare function parseAssignWithSeedInstruction<TProgram extends string, TAccountMetas extends readonly IAccountMeta[]>(instruction: IInstruction<TProgram> & IInstructionWithAccounts<TAccountMetas> & IInstructionWithData<Uint8Array>): ParsedAssignWithSeedInstruction<TProgram, TAccountMetas>;
//# sourceMappingURL=assignWithSeed.d.ts.map