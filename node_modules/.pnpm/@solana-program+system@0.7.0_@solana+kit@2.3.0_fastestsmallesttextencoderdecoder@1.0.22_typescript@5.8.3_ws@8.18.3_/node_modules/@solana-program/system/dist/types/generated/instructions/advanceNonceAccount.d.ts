/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IAccountSignerMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData, type ReadonlyAccount, type ReadonlySignerAccount, type TransactionSigner, type WritableAccount } from '@solana/kit';
import { SYSTEM_PROGRAM_ADDRESS } from '../programs';
export declare const ADVANCE_NONCE_ACCOUNT_DISCRIMINATOR = 4;
export declare function getAdvanceNonceAccountDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type AdvanceNonceAccountInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountNonceAccount extends string | IAccountMeta<string> = string, TAccountRecentBlockhashesSysvar extends string | IAccountMeta<string> = 'SysvarRecentB1ockHashes11111111111111111111', TAccountNonceAuthority extends string | IAccountMeta<string> = string, TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<[
    TAccountNonceAccount extends string ? WritableAccount<TAccountNonceAccount> : TAccountNonceAccount,
    TAccountRecentBlockhashesSysvar extends string ? ReadonlyAccount<TAccountRecentBlockhashesSysvar> : TAccountRecentBlockhashesSysvar,
    TAccountNonceAuthority extends string ? ReadonlySignerAccount<TAccountNonceAuthority> & IAccountSignerMeta<TAccountNonceAuthority> : TAccountNonceAuthority,
    ...TRemainingAccounts
]>;
export type AdvanceNonceAccountInstructionData = {
    discriminator: number;
};
export type AdvanceNonceAccountInstructionDataArgs = {};
export declare function getAdvanceNonceAccountInstructionDataEncoder(): Encoder<AdvanceNonceAccountInstructionDataArgs>;
export declare function getAdvanceNonceAccountInstructionDataDecoder(): Decoder<AdvanceNonceAccountInstructionData>;
export declare function getAdvanceNonceAccountInstructionDataCodec(): Codec<AdvanceNonceAccountInstructionDataArgs, AdvanceNonceAccountInstructionData>;
export type AdvanceNonceAccountInput<TAccountNonceAccount extends string = string, TAccountRecentBlockhashesSysvar extends string = string, TAccountNonceAuthority extends string = string> = {
    nonceAccount: Address<TAccountNonceAccount>;
    recentBlockhashesSysvar?: Address<TAccountRecentBlockhashesSysvar>;
    nonceAuthority: TransactionSigner<TAccountNonceAuthority>;
};
export declare function getAdvanceNonceAccountInstruction<TAccountNonceAccount extends string, TAccountRecentBlockhashesSysvar extends string, TAccountNonceAuthority extends string, TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS>(input: AdvanceNonceAccountInput<TAccountNonceAccount, TAccountRecentBlockhashesSysvar, TAccountNonceAuthority>, config?: {
    programAddress?: TProgramAddress;
}): AdvanceNonceAccountInstruction<TProgramAddress, TAccountNonceAccount, TAccountRecentBlockhashesSysvar, TAccountNonceAuthority>;
export type ParsedAdvanceNonceAccountInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[]> = {
    programAddress: Address<TProgram>;
    accounts: {
        nonceAccount: TAccountMetas[0];
        recentBlockhashesSysvar: TAccountMetas[1];
        nonceAuthority: TAccountMetas[2];
    };
    data: AdvanceNonceAccountInstructionData;
};
export declare function parseAdvanceNonceAccountInstruction<TProgram extends string, TAccountMetas extends readonly IAccountMeta[]>(instruction: IInstruction<TProgram> & IInstructionWithAccounts<TAccountMetas> & IInstructionWithData<Uint8Array>): ParsedAdvanceNonceAccountInstruction<TProgram, TAccountMetas>;
//# sourceMappingURL=advanceNonceAccount.d.ts.map