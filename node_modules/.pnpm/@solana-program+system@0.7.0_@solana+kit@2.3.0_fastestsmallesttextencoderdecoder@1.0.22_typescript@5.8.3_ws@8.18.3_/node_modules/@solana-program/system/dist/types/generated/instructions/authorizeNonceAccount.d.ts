/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IAccountSignerMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData, type ReadonlySignerAccount, type TransactionSigner, type WritableAccount } from '@solana/kit';
import { SYSTEM_PROGRAM_ADDRESS } from '../programs';
export declare const AUTHORIZE_NONCE_ACCOUNT_DISCRIMINATOR = 7;
export declare function getAuthorizeNonceAccountDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type AuthorizeNonceAccountInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountNonceAccount extends string | IAccountMeta<string> = string, TAccountNonceAuthority extends string | IAccountMeta<string> = string, TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<[
    TAccountNonceAccount extends string ? WritableAccount<TAccountNonceAccount> : TAccountNonceAccount,
    TAccountNonceAuthority extends string ? ReadonlySignerAccount<TAccountNonceAuthority> & IAccountSignerMeta<TAccountNonceAuthority> : TAccountNonceAuthority,
    ...TRemainingAccounts
]>;
export type AuthorizeNonceAccountInstructionData = {
    discriminator: number;
    newNonceAuthority: Address;
};
export type AuthorizeNonceAccountInstructionDataArgs = {
    newNonceAuthority: Address;
};
export declare function getAuthorizeNonceAccountInstructionDataEncoder(): Encoder<AuthorizeNonceAccountInstructionDataArgs>;
export declare function getAuthorizeNonceAccountInstructionDataDecoder(): Decoder<AuthorizeNonceAccountInstructionData>;
export declare function getAuthorizeNonceAccountInstructionDataCodec(): Codec<AuthorizeNonceAccountInstructionDataArgs, AuthorizeNonceAccountInstructionData>;
export type AuthorizeNonceAccountInput<TAccountNonceAccount extends string = string, TAccountNonceAuthority extends string = string> = {
    nonceAccount: Address<TAccountNonceAccount>;
    nonceAuthority: TransactionSigner<TAccountNonceAuthority>;
    newNonceAuthority: AuthorizeNonceAccountInstructionDataArgs['newNonceAuthority'];
};
export declare function getAuthorizeNonceAccountInstruction<TAccountNonceAccount extends string, TAccountNonceAuthority extends string, TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS>(input: AuthorizeNonceAccountInput<TAccountNonceAccount, TAccountNonceAuthority>, config?: {
    programAddress?: TProgramAddress;
}): AuthorizeNonceAccountInstruction<TProgramAddress, TAccountNonceAccount, TAccountNonceAuthority>;
export type ParsedAuthorizeNonceAccountInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[]> = {
    programAddress: Address<TProgram>;
    accounts: {
        nonceAccount: TAccountMetas[0];
        nonceAuthority: TAccountMetas[1];
    };
    data: AuthorizeNonceAccountInstructionData;
};
export declare function parseAuthorizeNonceAccountInstruction<TProgram extends string, TAccountMetas extends readonly IAccountMeta[]>(instruction: IInstruction<TProgram> & IInstructionWithAccounts<TAccountMetas> & IInstructionWithData<Uint8Array>): ParsedAuthorizeNonceAccountInstruction<TProgram, TAccountMetas>;
//# sourceMappingURL=authorizeNonceAccount.d.ts.map