/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IAccountSignerMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData, type TransactionSigner, type WritableAccount, type WritableSignerAccount } from '@solana/kit';
import { SYSTEM_PROGRAM_ADDRESS } from '../programs';
export declare const TRANSFER_SOL_DISCRIMINATOR = 2;
export declare function getTransferSolDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type TransferSolInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountSource extends string | IAccountMeta<string> = string, TAccountDestination extends string | IAccountMeta<string> = string, TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<[
    TAccountSource extends string ? WritableSignerAccount<TAccountSource> & IAccountSignerMeta<TAccountSource> : TAccountSource,
    TAccountDestination extends string ? WritableAccount<TAccountDestination> : TAccountDestination,
    ...TRemainingAccounts
]>;
export type TransferSolInstructionData = {
    discriminator: number;
    amount: bigint;
};
export type TransferSolInstructionDataArgs = {
    amount: number | bigint;
};
export declare function getTransferSolInstructionDataEncoder(): Encoder<TransferSolInstructionDataArgs>;
export declare function getTransferSolInstructionDataDecoder(): Decoder<TransferSolInstructionData>;
export declare function getTransferSolInstructionDataCodec(): Codec<TransferSolInstructionDataArgs, TransferSolInstructionData>;
export type TransferSolInput<TAccountSource extends string = string, TAccountDestination extends string = string> = {
    source: TransactionSigner<TAccountSource>;
    destination: Address<TAccountDestination>;
    amount: TransferSolInstructionDataArgs['amount'];
};
export declare function getTransferSolInstruction<TAccountSource extends string, TAccountDestination extends string, TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS>(input: TransferSolInput<TAccountSource, TAccountDestination>, config?: {
    programAddress?: TProgramAddress;
}): TransferSolInstruction<TProgramAddress, TAccountSource, TAccountDestination>;
export type ParsedTransferSolInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[]> = {
    programAddress: Address<TProgram>;
    accounts: {
        source: TAccountMetas[0];
        destination: TAccountMetas[1];
    };
    data: TransferSolInstructionData;
};
export declare function parseTransferSolInstruction<TProgram extends string, TAccountMetas extends readonly IAccountMeta[]>(instruction: IInstruction<TProgram> & IInstructionWithAccounts<TAccountMetas> & IInstructionWithData<Uint8Array>): ParsedTransferSolInstruction<TProgram, TAccountMetas>;
//# sourceMappingURL=transferSol.d.ts.map