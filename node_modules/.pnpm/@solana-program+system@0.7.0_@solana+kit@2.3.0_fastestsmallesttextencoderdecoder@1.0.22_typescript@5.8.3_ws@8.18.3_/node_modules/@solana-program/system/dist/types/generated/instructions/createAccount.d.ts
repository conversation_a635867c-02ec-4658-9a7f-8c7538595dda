/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IAccountSignerMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData, type TransactionSigner, type WritableSignerAccount } from '@solana/kit';
import { SYSTEM_PROGRAM_ADDRESS } from '../programs';
import { type IInstructionWithByteDelta } from '../shared';
export declare const CREATE_ACCOUNT_DISCRIMINATOR = 0;
export declare function getCreateAccountDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type CreateAccountInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountPayer extends string | IAccountMeta<string> = string, TAccountNewAccount extends string | IAccountMeta<string> = string, TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<[
    TAccountPayer extends string ? WritableSignerAccount<TAccountPayer> & IAccountSignerMeta<TAccountPayer> : TAccountPayer,
    TAccountNewAccount extends string ? WritableSignerAccount<TAccountNewAccount> & IAccountSignerMeta<TAccountNewAccount> : TAccountNewAccount,
    ...TRemainingAccounts
]>;
export type CreateAccountInstructionData = {
    discriminator: number;
    lamports: bigint;
    space: bigint;
    programAddress: Address;
};
export type CreateAccountInstructionDataArgs = {
    lamports: number | bigint;
    space: number | bigint;
    programAddress: Address;
};
export declare function getCreateAccountInstructionDataEncoder(): Encoder<CreateAccountInstructionDataArgs>;
export declare function getCreateAccountInstructionDataDecoder(): Decoder<CreateAccountInstructionData>;
export declare function getCreateAccountInstructionDataCodec(): Codec<CreateAccountInstructionDataArgs, CreateAccountInstructionData>;
export type CreateAccountInput<TAccountPayer extends string = string, TAccountNewAccount extends string = string> = {
    payer: TransactionSigner<TAccountPayer>;
    newAccount: TransactionSigner<TAccountNewAccount>;
    lamports: CreateAccountInstructionDataArgs['lamports'];
    space: CreateAccountInstructionDataArgs['space'];
    programAddress: CreateAccountInstructionDataArgs['programAddress'];
};
export declare function getCreateAccountInstruction<TAccountPayer extends string, TAccountNewAccount extends string, TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS>(input: CreateAccountInput<TAccountPayer, TAccountNewAccount>, config?: {
    programAddress?: TProgramAddress;
}): CreateAccountInstruction<TProgramAddress, TAccountPayer, TAccountNewAccount> & IInstructionWithByteDelta;
export type ParsedCreateAccountInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[]> = {
    programAddress: Address<TProgram>;
    accounts: {
        payer: TAccountMetas[0];
        newAccount: TAccountMetas[1];
    };
    data: CreateAccountInstructionData;
};
export declare function parseCreateAccountInstruction<TProgram extends string, TAccountMetas extends readonly IAccountMeta[]>(instruction: IInstruction<TProgram> & IInstructionWithAccounts<TAccountMetas> & IInstructionWithData<Uint8Array>): ParsedCreateAccountInstruction<TProgram, TAccountMetas>;
//# sourceMappingURL=createAccount.d.ts.map