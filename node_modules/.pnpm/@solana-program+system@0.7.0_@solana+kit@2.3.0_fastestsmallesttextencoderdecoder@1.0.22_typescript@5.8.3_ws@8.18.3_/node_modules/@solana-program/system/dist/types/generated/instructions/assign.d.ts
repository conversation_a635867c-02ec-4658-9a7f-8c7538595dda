/**
 * This code was AUTOGENERATED using the codama library.
 * Please DO NOT EDIT THIS FILE, instead use visitors
 * to add features, then rerun codama to update it.
 *
 * @see https://github.com/codama-idl/codama
 */
import { type Address, type Codec, type Decoder, type Encoder, type IAccountMeta, type IAccountSignerMeta, type IInstruction, type IInstructionWithAccounts, type IInstructionWithData, type TransactionSigner, type WritableSignerAccount } from '@solana/kit';
import { SYSTEM_PROGRAM_ADDRESS } from '../programs';
export declare const ASSIGN_DISCRIMINATOR = 1;
export declare function getAssignDiscriminatorBytes(): import("@solana/kit").ReadonlyUint8Array;
export type AssignInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountAccount extends string | IAccountMeta<string> = string, TRemainingAccounts extends readonly IAccountMeta<string>[] = []> = IInstruction<TProgram> & IInstructionWithData<Uint8Array> & IInstructionWithAccounts<[
    TAccountAccount extends string ? WritableSignerAccount<TAccountAccount> & IAccountSignerMeta<TAccountAccount> : TAccountAccount,
    ...TRemainingAccounts
]>;
export type AssignInstructionData = {
    discriminator: number;
    programAddress: Address;
};
export type AssignInstructionDataArgs = {
    programAddress: Address;
};
export declare function getAssignInstructionDataEncoder(): Encoder<AssignInstructionDataArgs>;
export declare function getAssignInstructionDataDecoder(): Decoder<AssignInstructionData>;
export declare function getAssignInstructionDataCodec(): Codec<AssignInstructionDataArgs, AssignInstructionData>;
export type AssignInput<TAccountAccount extends string = string> = {
    account: TransactionSigner<TAccountAccount>;
    programAddress: AssignInstructionDataArgs['programAddress'];
};
export declare function getAssignInstruction<TAccountAccount extends string, TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS>(input: AssignInput<TAccountAccount>, config?: {
    programAddress?: TProgramAddress;
}): AssignInstruction<TProgramAddress, TAccountAccount>;
export type ParsedAssignInstruction<TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS, TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[]> = {
    programAddress: Address<TProgram>;
    accounts: {
        account: TAccountMetas[0];
    };
    data: AssignInstructionData;
};
export declare function parseAssignInstruction<TProgram extends string, TAccountMetas extends readonly IAccountMeta[]>(instruction: IInstruction<TProgram> & IInstructionWithAccounts<TAccountMetas> & IInstructionWithData<Uint8Array>): ParsedAssignInstruction<TProgram, TAccountMetas>;
//# sourceMappingURL=assign.d.ts.map