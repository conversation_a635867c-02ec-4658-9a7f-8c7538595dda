{"version": 3, "sources": ["../../src/generated/types/nonceState.ts", "../../src/generated/types/nonceVersion.ts", "../../src/generated/accounts/nonce.ts", "../../src/generated/programs/system.ts", "../../src/generated/errors/system.ts", "../../src/generated/shared/index.ts", "../../src/generated/instructions/advanceNonceAccount.ts", "../../src/generated/instructions/allocate.ts", "../../src/generated/instructions/allocateWithSeed.ts", "../../src/generated/instructions/assign.ts", "../../src/generated/instructions/assignWithSeed.ts", "../../src/generated/instructions/authorizeNonceAccount.ts", "../../src/generated/instructions/createAccount.ts", "../../src/generated/instructions/createAccountWithSeed.ts", "../../src/generated/instructions/initializeNonceAccount.ts", "../../src/generated/instructions/transferSol.ts", "../../src/generated/instructions/transferSolWithSeed.ts", "../../src/generated/instructions/upgradeNonceAccount.ts", "../../src/generated/instructions/withdrawNonceAccount.ts"], "names": ["NonceState", "NonceVersion", "getEnumEncoder", "getU32Encoder", "getEnumDecoder", "getU32Decoder", "combineCodec", "SystemAccount", "SystemInstruction", "kitIsTransactionSigner", "getStructEncoder", "getStructDecoder", "transformEncoder", "getU64Encoder", "getU64Decoder", "getAddressEncoder", "getAddressDecoder", "addEncoderSizePrefix", "getUtf8Encoder", "addDecoderSizePrefix", "getUtf8Decoder"], "mappings": ";;;AAmBY,IAAA,UAAA,qBAAAA,WAAL,KAAA;AACL,EAAAA,WAAA,CAAA,WAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAA,CAAA;AACA,EAAAA,WAAA,CAAA,WAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAA,CAAA;AAFU,EAAAA,OAAAA,WAAAA,CAAAA;AAAA,CAAA,EAAA,UAAA,IAAA,EAAA,EAAA;AAOL,SAAS,oBAAgD,GAAA;AAC9D,EAAA,OAAO,eAAe,UAAY,EAAA,EAAE,IAAM,EAAA,aAAA,IAAiB,CAAA,CAAA;AAC7D,CAAA;AAEO,SAAS,oBAA4C,GAAA;AAC1D,EAAA,OAAO,eAAe,UAAY,EAAA,EAAE,IAAM,EAAA,aAAA,IAAiB,CAAA,CAAA;AAC7D,CAAA;AAEO,SAAS,kBAAwD,GAAA;AACtE,EAAA,OAAO,YAAa,CAAA,oBAAA,EAAwB,EAAA,oBAAA,EAAsB,CAAA,CAAA;AACpE,CAAA;ACjBY,IAAA,YAAA,qBAAAC,aAAL,KAAA;AACL,EAAAA,aAAA,CAAA,aAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAA,CAAA;AACA,EAAAA,aAAA,CAAA,aAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAA,CAAA;AAFU,EAAAA,OAAAA,aAAAA,CAAAA;AAAA,CAAA,EAAA,YAAA,IAAA,EAAA,EAAA;AAOL,SAAS,sBAAoD,GAAA;AAClE,EAAA,OAAOC,eAAe,YAAc,EAAA,EAAE,IAAMC,EAAAA,aAAAA,IAAiB,CAAA,CAAA;AAC/D,CAAA;AAEO,SAAS,sBAAgD,GAAA;AAC9D,EAAA,OAAOC,eAAe,YAAc,EAAA,EAAE,IAAMC,EAAAA,aAAAA,IAAiB,CAAA,CAAA;AAC/D,CAAA;AAEO,SAAS,oBAA8D,GAAA;AAC5E,EAAA,OAAOC,YAAa,CAAA,sBAAA,EAA0B,EAAA,sBAAA,EAAwB,CAAA,CAAA;AACxE,CAAA;;;ACuBO,SAAS,eAAsC,GAAA;AACpD,EAAA,OAAO,gBAAiB,CAAA;AAAA,IACtB,CAAC,SAAW,EAAA,sBAAA,EAAwB,CAAA;AAAA,IACpC,CAAC,OAAS,EAAA,oBAAA,EAAsB,CAAA;AAAA,IAChC,CAAC,WAAa,EAAA,iBAAA,EAAmB,CAAA;AAAA,IACjC,CAAC,WAAa,EAAA,iBAAA,EAAmB,CAAA;AAAA,IACjC,CAAC,sBAAwB,EAAA,aAAA,EAAe,CAAA;AAAA,GACzC,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,eAAkC,GAAA;AAChD,EAAA,OAAO,gBAAiB,CAAA;AAAA,IACtB,CAAC,SAAW,EAAA,sBAAA,EAAwB,CAAA;AAAA,IACpC,CAAC,OAAS,EAAA,oBAAA,EAAsB,CAAA;AAAA,IAChC,CAAC,WAAa,EAAA,iBAAA,EAAmB,CAAA;AAAA,IACjC,CAAC,WAAa,EAAA,iBAAA,EAAmB,CAAA;AAAA,IACjC,CAAC,sBAAwB,EAAA,aAAA,EAAe,CAAA;AAAA,GACzC,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,aAAyC,GAAA;AACvD,EAAA,OAAOA,YAAa,CAAA,eAAA,EAAmB,EAAA,eAAA,EAAiB,CAAA,CAAA;AAC1D,CAAA;AAQO,SAAS,YACd,cAC0D,EAAA;AAC1D,EAAO,OAAA,aAAA;AAAA,IACL,cAAA;AAAA,IACA,eAAgB,EAAA;AAAA,GAClB,CAAA;AACF,CAAA;AAEA,eAAsB,UAAA,CACpB,GACA,EAAA,OAAA,EACA,MACmC,EAAA;AACnC,EAAA,MAAM,YAAe,GAAA,MAAM,eAAgB,CAAA,GAAA,EAAK,SAAS,MAAM,CAAA,CAAA;AAC/D,EAAA,mBAAA,CAAoB,YAAY,CAAA,CAAA;AAChC,EAAO,OAAA,YAAA,CAAA;AACT,CAAA;AAEA,eAAsB,eAAA,CACpB,GACA,EAAA,OAAA,EACA,MACwC,EAAA;AACxC,EAAA,MAAM,YAAe,GAAA,MAAM,mBAAoB,CAAA,GAAA,EAAK,SAAS,MAAM,CAAA,CAAA;AACnE,EAAA,OAAO,YAAY,YAAY,CAAA,CAAA;AACjC,CAAA;AAEA,eAAsB,aAAA,CACpB,GACA,EAAA,SAAA,EACA,MAC2B,EAAA;AAC3B,EAAA,MAAM,aAAgB,GAAA,MAAM,kBAAmB,CAAA,GAAA,EAAK,WAAW,MAAM,CAAA,CAAA;AACrE,EAAA,mBAAA,CAAoB,aAAa,CAAA,CAAA;AACjC,EAAO,OAAA,aAAA,CAAA;AACT,CAAA;AAEA,eAAsB,kBAAA,CACpB,GACA,EAAA,SAAA,EACA,MACgC,EAAA;AAChC,EAAA,MAAM,aAAgB,GAAA,MAAM,oBAAqB,CAAA,GAAA,EAAK,WAAW,MAAM,CAAA,CAAA;AACvE,EAAA,OAAO,cAAc,GAAI,CAAA,CAAC,YAAiB,KAAA,WAAA,CAAY,YAAY,CAAC,CAAA,CAAA;AACtE,CAAA;AAEO,SAAS,YAAuB,GAAA;AACrC,EAAO,OAAA,EAAA,CAAA;AACT,CAAA;AC5GO,IAAM,sBACX,GAAA,mCAAA;AAEU,IAAA,aAAA,qBAAAC,cAAL,KAAA;AACL,EAAAA,cAAA,CAAA,cAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAA,CAAA;AADU,EAAAA,OAAAA,cAAAA,CAAAA;AAAA,CAAA,EAAA,aAAA,IAAA,EAAA,EAAA;AAIA,IAAA,iBAAA,qBAAAC,kBAAL,KAAA;AACL,EAAAA,kBAAA,CAAA,kBAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAA,CAAA;AACA,EAAAA,kBAAA,CAAA,kBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAA,CAAA;AACA,EAAAA,kBAAA,CAAA,kBAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAA,CAAA;AACA,EAAAA,kBAAA,CAAA,kBAAA,CAAA,uBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,uBAAA,CAAA;AACA,EAAAA,kBAAA,CAAA,kBAAA,CAAA,qBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,qBAAA,CAAA;AACA,EAAAA,kBAAA,CAAA,kBAAA,CAAA,sBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,sBAAA,CAAA;AACA,EAAAA,kBAAA,CAAA,kBAAA,CAAA,wBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,wBAAA,CAAA;AACA,EAAAA,kBAAA,CAAA,kBAAA,CAAA,uBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,uBAAA,CAAA;AACA,EAAAA,kBAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAA,CAAA;AACA,EAAAA,kBAAA,CAAA,kBAAA,CAAA,kBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,kBAAA,CAAA;AACA,EAAAA,kBAAA,CAAA,kBAAA,CAAA,gBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,gBAAA,CAAA;AACA,EAAAA,kBAAA,CAAA,kBAAA,CAAA,qBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,qBAAA,CAAA;AACA,EAAAA,kBAAA,CAAA,kBAAA,CAAA,qBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,qBAAA,CAAA;AAbU,EAAAA,OAAAA,kBAAAA,CAAAA;AAAA,CAAA,EAAA,iBAAA,IAAA,EAAA,EAAA;AAgBL,SAAS,0BACd,WACmB,EAAA;AACnB,EAAA,MAAM,IAAO,GAAA,MAAA,IAAU,WAAc,GAAA,WAAA,CAAY,IAAO,GAAA,WAAA,CAAA;AACxD,EAAI,IAAA,aAAA,CAAc,MAAML,aAAc,EAAA,CAAE,OAAO,CAAC,CAAA,EAAG,CAAC,CAAG,EAAA;AACrD,IAAO,OAAA,CAAA,qBAAA;AAAA,GACT;AACA,EAAI,IAAA,aAAA,CAAc,MAAMA,aAAc,EAAA,CAAE,OAAO,CAAC,CAAA,EAAG,CAAC,CAAG,EAAA;AACrD,IAAO,OAAA,CAAA,cAAA;AAAA,GACT;AACA,EAAI,IAAA,aAAA,CAAc,MAAMA,aAAc,EAAA,CAAE,OAAO,CAAC,CAAA,EAAG,CAAC,CAAG,EAAA;AACrD,IAAO,OAAA,CAAA,mBAAA;AAAA,GACT;AACA,EAAI,IAAA,aAAA,CAAc,MAAMA,aAAc,EAAA,CAAE,OAAO,CAAC,CAAA,EAAG,CAAC,CAAG,EAAA;AACrD,IAAO,OAAA,CAAA,6BAAA;AAAA,GACT;AACA,EAAI,IAAA,aAAA,CAAc,MAAMA,aAAc,EAAA,CAAE,OAAO,CAAC,CAAA,EAAG,CAAC,CAAG,EAAA;AACrD,IAAO,OAAA,CAAA,2BAAA;AAAA,GACT;AACA,EAAI,IAAA,aAAA,CAAc,MAAMA,aAAc,EAAA,CAAE,OAAO,CAAC,CAAA,EAAG,CAAC,CAAG,EAAA;AACrD,IAAO,OAAA,CAAA,4BAAA;AAAA,GACT;AACA,EAAI,IAAA,aAAA,CAAc,MAAMA,aAAc,EAAA,CAAE,OAAO,CAAC,CAAA,EAAG,CAAC,CAAG,EAAA;AACrD,IAAO,OAAA,CAAA,8BAAA;AAAA,GACT;AACA,EAAI,IAAA,aAAA,CAAc,MAAMA,aAAc,EAAA,CAAE,OAAO,CAAC,CAAA,EAAG,CAAC,CAAG,EAAA;AACrD,IAAO,OAAA,CAAA,6BAAA;AAAA,GACT;AACA,EAAI,IAAA,aAAA,CAAc,MAAMA,aAAc,EAAA,CAAE,OAAO,CAAC,CAAA,EAAG,CAAC,CAAG,EAAA;AACrD,IAAO,OAAA,CAAA,gBAAA;AAAA,GACT;AACA,EAAI,IAAA,aAAA,CAAc,MAAMA,aAAc,EAAA,CAAE,OAAO,CAAC,CAAA,EAAG,CAAC,CAAG,EAAA;AACrD,IAAO,OAAA,CAAA,wBAAA;AAAA,GACT;AACA,EAAI,IAAA,aAAA,CAAc,MAAMA,aAAc,EAAA,CAAE,OAAO,EAAE,CAAA,EAAG,CAAC,CAAG,EAAA;AACtD,IAAO,OAAA,EAAA,sBAAA;AAAA,GACT;AACA,EAAI,IAAA,aAAA,CAAc,MAAMA,aAAc,EAAA,CAAE,OAAO,EAAE,CAAA,EAAG,CAAC,CAAG,EAAA;AACtD,IAAO,OAAA,EAAA,2BAAA;AAAA,GACT;AACA,EAAI,IAAA,aAAA,CAAc,MAAMA,aAAc,EAAA,CAAE,OAAO,EAAE,CAAA,EAAG,CAAC,CAAG,EAAA;AACtD,IAAO,OAAA,EAAA,2BAAA;AAAA,GACT;AACA,EAAA,MAAM,IAAI,KAAA;AAAA,IACR,2EAAA;AAAA,GACF,CAAA;AACF,CAAA;;;AClFO,IAAM,oCAAuC,GAAA,EAAA;AAE7C,IAAM,2CAA8C,GAAA,EAAA;AAEpD,IAAM,gCAAmC,GAAA,EAAA;AAEzC,IAAM,yCAA4C,GAAA,EAAA;AAElD,IAAM,sCAAyC,GAAA,EAAA;AAE/C,IAAM,wCAA2C,GAAA,EAAA;AAEjD,IAAM,yCAA4C,GAAA,EAAA;AAElD,IAAM,yCAA4C,GAAA,EAAA;AAElD,IAAM,8CAAiD,GAAA,EAAA;AAa9D,IAAI,mBAAA,CAAA;AACJ,IAAI,OAAA,CAAQ,GAAI,CAAA,QAAA,KAAa,YAAc,EAAA;AACzC,EAAsB,mBAAA,GAAA;AAAA,IACpB,CAAC,oCAAoC,GAAG,CAAA,+CAAA,CAAA;AAAA,IACxC,CAAC,wCAAwC,GAAG,CAAA,2DAAA,CAAA;AAAA,IAC5C,CAAC,yCAAyC,GAAG,CAAA,2CAAA,CAAA;AAAA,IAC7C,CAAC,gCAAgC,GAAG,CAAA,wCAAA,CAAA;AAAA,IACpC,CAAC,sCAAsC,GAAG,CAAA,oCAAA,CAAA;AAAA,IAC1C,CAAC,yCAAyC,GAAG,CAAA,2CAAA,CAAA;AAAA,IAC7C,CAAC,yCAAyC,GAAG,CAAA,oEAAA,CAAA;AAAA,IAC7C,CAAC,8CAA8C,GAAG,CAAA,2CAAA,CAAA;AAAA,IAClD,CAAC,2CAA2C,GAAG,CAAA,yDAAA,CAAA;AAAA,GACjD,CAAA;AACF,CAAA;AAEO,SAAS,sBAAsB,IAA2B,EAAA;AAC/D,EAAI,IAAA,OAAA,CAAQ,GAAI,CAAA,QAAA,KAAa,YAAc,EAAA;AACzC,IAAA,OAAQ,oBAAoD,IAAI,CAAA,CAAA;AAAA,GAClE;AAEA,EAAO,OAAA,oDAAA,CAAA;AACT,CAAA;AAEO,SAAS,aAAA,CACd,KACA,EAAA,kBAAA,EAGA,IAE6D,EAAA;AAC7D,EAAO,OAAA,cAAA;AAAA,IACL,KAAA;AAAA,IACA,kBAAA;AAAA,IACA,sBAAA;AAAA,IACA,IAAA;AAAA,GACF,CAAA;AACF,CAAA;AChDO,SAAS,cACd,KAMY,EAAA;AACZ,EAAA,IAAI,CAAC,KAAO,EAAA;AACV,IAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA,CAAA;AAAA,GACvC;AACA,EAAA,IAAI,OAAO,KAAA,KAAU,QAAY,IAAA,SAAA,IAAa,KAAO,EAAA;AACnD,IAAA,OAAO,KAAM,CAAA,OAAA,CAAA;AAAA,GACf;AACA,EAAI,IAAA,KAAA,CAAM,OAAQ,CAAA,KAAK,CAAG,EAAA;AACxB,IAAA,OAAO,MAAM,CAAC,CAAA,CAAA;AAAA,GAChB;AACA,EAAO,OAAA,KAAA,CAAA;AACT,CAAA;AAsEO,SAAS,qBAAA,CACd,gBACA,uBACA,EAAA;AACA,EAAA,OAAO,CACL,OACkD,KAAA;AAClD,IAAI,IAAA,CAAC,QAAQ,KAAO,EAAA;AAElB,MAAA,OAAO,OAAO,MAAO,CAAA;AAAA,QACnB,OAAS,EAAA,cAAA;AAAA,QACT,MAAM,WAAY,CAAA,QAAA;AAAA,OACnB,CAAA,CAAA;AAAA,KACH;AAEA,IAAA,MAAM,YAAe,GAAA,OAAA,CAAQ,UACzB,GAAA,WAAA,CAAY,WACZ,WAAY,CAAA,QAAA,CAAA;AAChB,IAAA,OAAO,OAAO,MAAO,CAAA;AAAA,MACnB,OAAA,EAAS,aAAc,CAAA,OAAA,CAAQ,KAAK,CAAA;AAAA,MACpC,MAAM,mBAAoB,CAAA,OAAA,CAAQ,KAAK,CACnC,GAAA,mBAAA,CAAoB,YAAY,CAChC,GAAA,YAAA;AAAA,MACJ,GAAI,mBAAoB,CAAA,OAAA,CAAQ,KAAK,CAAA,GAAI,EAAE,MAAQ,EAAA,OAAA,CAAQ,KAAM,EAAA,GAAI,EAAC;AAAA,KACvE,CAAA,CAAA;AAAA,GACH,CAAA;AACF,CAAA;AAEO,SAAS,oBACd,KAIsC,EAAA;AACtC,EACE,OAAA,CAAC,CAAC,KACF,IAAA,OAAO,UAAU,QACjB,IAAA,SAAA,IAAa,KACb,IAAAM,qBAAA,CAAuB,KAAK,CAAA,CAAA;AAEhC,CAAA;;;ACnIO,IAAM,mCAAsC,GAAA,EAAA;AAE5C,SAAS,wCAA2C,GAAA;AACzD,EAAON,OAAAA,aAAAA,EAAgB,CAAA,MAAA,CAAO,mCAAmC,CAAA,CAAA;AACnE,CAAA;AAgCO,SAAS,4CAAgG,GAAA;AAC9G,EAAO,OAAA,gBAAA;AAAA,IACLO,iBAAiB,CAAC,CAAC,iBAAiBP,aAAc,EAAC,CAAC,CAAC,CAAA;AAAA,IACrD,CAAC,KAAW,MAAA;AAAA,MACV,GAAG,KAAA;AAAA,MACH,aAAe,EAAA,mCAAA;AAAA,KACjB,CAAA;AAAA,GACF,CAAA;AACF,CAAA;AAEO,SAAS,4CAA4F,GAAA;AAC1G,EAAA,OAAOQ,iBAAiB,CAAC,CAAC,iBAAiBN,aAAc,EAAC,CAAC,CAAC,CAAA,CAAA;AAC9D,CAAA;AAEO,SAAS,0CAGd,GAAA;AACA,EAAOC,OAAAA,YAAAA;AAAA,IACL,4CAA6C,EAAA;AAAA,IAC7C,4CAA6C,EAAA;AAAA,GAC/C,CAAA;AACF,CAAA;AAYO,SAAS,iCAAA,CAMd,OAKA,MAMA,EAAA;AAEA,EAAM,MAAA,cAAA,GAAiB,QAAQ,cAAkB,IAAA,sBAAA,CAAA;AAGjD,EAAA,MAAM,gBAAmB,GAAA;AAAA,IACvB,cAAc,EAAE,KAAA,EAAO,MAAM,YAAgB,IAAA,IAAA,EAAM,YAAY,IAAK,EAAA;AAAA,IACpE,uBAAyB,EAAA;AAAA,MACvB,KAAA,EAAO,MAAM,uBAA2B,IAAA,IAAA;AAAA,MACxC,UAAY,EAAA,KAAA;AAAA,KACd;AAAA,IACA,gBAAgB,EAAE,KAAA,EAAO,MAAM,cAAkB,IAAA,IAAA,EAAM,YAAY,KAAM,EAAA;AAAA,GAC3E,CAAA;AACA,EAAA,MAAM,QAAW,GAAA,gBAAA,CAAA;AAMjB,EAAI,IAAA,CAAC,QAAS,CAAA,uBAAA,CAAwB,KAAO,EAAA;AAC3C,IAAA,QAAA,CAAS,wBAAwB,KAC/B,GAAA,6CAAA,CAAA;AAAA,GACJ;AAEA,EAAM,MAAA,cAAA,GAAiB,qBAAsB,CAAA,cAA2B,CAAA,CAAA;AACxE,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,QAAU,EAAA;AAAA,MACR,cAAA,CAAe,SAAS,YAAY,CAAA;AAAA,MACpC,cAAA,CAAe,SAAS,uBAAuB,CAAA;AAAA,MAC/C,cAAA,CAAe,SAAS,cAAc,CAAA;AAAA,KACxC;AAAA,IACA,cAAA;AAAA,IACA,IAAM,EAAA,4CAAA,EAA+C,CAAA,MAAA,CAAO,EAAE,CAAA;AAAA,GAChE,CAAA;AAOA,EAAO,OAAA,WAAA,CAAA;AACT,CAAA;AAeO,SAAS,oCAId,WAG+D,EAAA;AAC/D,EAAI,IAAA,WAAA,CAAY,QAAS,CAAA,MAAA,GAAS,CAAG,EAAA;AAEnC,IAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA,CAAA;AAAA,GACvC;AACA,EAAA,IAAI,YAAe,GAAA,CAAA,CAAA;AACnB,EAAA,MAAM,iBAAiB,MAAM;AAC3B,IAAM,MAAA,WAAA,GAAc,WAAY,CAAA,QAAA,CAAU,YAAY,CAAA,CAAA;AACtD,IAAgB,YAAA,IAAA,CAAA,CAAA;AAChB,IAAO,OAAA,WAAA,CAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,QAAU,EAAA;AAAA,MACR,cAAc,cAAe,EAAA;AAAA,MAC7B,yBAAyB,cAAe,EAAA;AAAA,MACxC,gBAAgB,cAAe,EAAA;AAAA,KACjC;AAAA,IACA,IAAA,EAAM,8CAA+C,CAAA,MAAA;AAAA,MACnD,WAAY,CAAA,IAAA;AAAA,KACd;AAAA,GACF,CAAA;AACF,CAAA;AC5KO,IAAM,sBAAyB,GAAA,EAAA;AAE/B,SAAS,6BAAgC,GAAA;AAC9C,EAAOH,OAAAA,aAAAA,EAAgB,CAAA,MAAA,CAAO,sBAAsB,CAAA,CAAA;AACtD,CAAA;AAsBO,SAAS,iCAA0E,GAAA;AACxF,EAAOS,OAAAA,gBAAAA;AAAA,IACLF,gBAAiB,CAAA;AAAA,MACf,CAAC,eAAiBP,EAAAA,aAAAA,EAAe,CAAA;AAAA,MACjC,CAAC,OAASU,EAAAA,aAAAA,EAAe,CAAA;AAAA,KAC1B,CAAA;AAAA,IACD,CAAC,KAAW,MAAA,EAAE,GAAG,KAAA,EAAO,eAAe,sBAAuB,EAAA,CAAA;AAAA,GAChE,CAAA;AACF,CAAA;AAEO,SAAS,iCAAsE,GAAA;AACpF,EAAA,OAAOF,gBAAiB,CAAA;AAAA,IACtB,CAAC,eAAiBN,EAAAA,aAAAA,EAAe,CAAA;AAAA,IACjC,CAAC,OAASS,EAAAA,aAAAA,EAAe,CAAA;AAAA,GAC1B,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,+BAGd,GAAA;AACA,EAAOR,OAAAA,YAAAA;AAAA,IACL,iCAAkC,EAAA;AAAA,IAClC,iCAAkC,EAAA;AAAA,GACpC,CAAA;AACF,CAAA;AAOO,SAAS,sBAAA,CAId,OACA,MAC0D,EAAA;AAE1D,EAAM,MAAA,cAAA,GAAiB,QAAQ,cAAkB,IAAA,sBAAA,CAAA;AAGjD,EAAA,MAAM,gBAAmB,GAAA;AAAA,IACvB,YAAY,EAAE,KAAA,EAAO,MAAM,UAAc,IAAA,IAAA,EAAM,YAAY,IAAK,EAAA;AAAA,GAClE,CAAA;AACA,EAAA,MAAM,QAAW,GAAA,gBAAA,CAAA;AAMjB,EAAM,MAAA,IAAA,GAAO,EAAE,GAAG,KAAM,EAAA,CAAA;AAExB,EAAM,MAAA,cAAA,GAAiB,qBAAsB,CAAA,cAA2B,CAAA,CAAA;AACxE,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,QAAU,EAAA,CAAC,cAAe,CAAA,QAAA,CAAS,UAAU,CAAC,CAAA;AAAA,IAC9C,cAAA;AAAA,IACA,IAAA,EAAM,mCAAoC,CAAA,MAAA;AAAA,MACxC,IAAA;AAAA,KACF;AAAA,GACF,CAAA;AAEA,EAAO,OAAA,WAAA,CAAA;AACT,CAAA;AAaO,SAAS,yBAId,WAGoD,EAAA;AACpD,EAAI,IAAA,WAAA,CAAY,QAAS,CAAA,MAAA,GAAS,CAAG,EAAA;AAEnC,IAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA,CAAA;AAAA,GACvC;AACA,EAAA,IAAI,YAAe,GAAA,CAAA,CAAA;AACnB,EAAA,MAAM,iBAAiB,MAAM;AAC3B,IAAM,MAAA,WAAA,GAAc,WAAY,CAAA,QAAA,CAAU,YAAY,CAAA,CAAA;AACtD,IAAgB,YAAA,IAAA,CAAA,CAAA;AAChB,IAAO,OAAA,WAAA,CAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,QAAU,EAAA;AAAA,MACR,YAAY,cAAe,EAAA;AAAA,KAC7B;AAAA,IACA,IAAM,EAAA,iCAAA,EAAoC,CAAA,MAAA,CAAO,YAAY,IAAI,CAAA;AAAA,GACnE,CAAA;AACF,CAAA;ACzHO,IAAM,gCAAmC,GAAA,EAAA;AAEzC,SAAS,qCAAwC,GAAA;AACtD,EAAOH,OAAAA,aAAAA,EAAgB,CAAA,MAAA,CAAO,gCAAgC,CAAA,CAAA;AAChE,CAAA;AAqCO,SAAS,yCAA0F,GAAA;AACxG,EAAOS,OAAAA,gBAAAA;AAAA,IACLF,gBAAiB,CAAA;AAAA,MACf,CAAC,eAAiBP,EAAAA,aAAAA,EAAe,CAAA;AAAA,MACjC,CAAC,MAAQY,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,MAC5B,CAAC,MAAQ,EAAA,oBAAA,CAAqB,gBAAkBF,EAAAA,aAAAA,EAAe,CAAC,CAAA;AAAA,MAChE,CAAC,OAASA,EAAAA,aAAAA,EAAe,CAAA;AAAA,MACzB,CAAC,gBAAkBE,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,KACvC,CAAA;AAAA,IACD,CAAC,KAAW,MAAA,EAAE,GAAG,KAAA,EAAO,eAAe,gCAAiC,EAAA,CAAA;AAAA,GAC1E,CAAA;AACF,CAAA;AAEO,SAAS,yCAAsF,GAAA;AACpG,EAAA,OAAOJ,gBAAiB,CAAA;AAAA,IACtB,CAAC,eAAiBN,EAAAA,aAAAA,EAAe,CAAA;AAAA,IACjC,CAAC,MAAQW,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,IAC5B,CAAC,MAAQ,EAAA,oBAAA,CAAqB,gBAAkBF,EAAAA,aAAAA,EAAe,CAAC,CAAA;AAAA,IAChE,CAAC,OAASA,EAAAA,aAAAA,EAAe,CAAA;AAAA,IACzB,CAAC,gBAAkBE,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,GACvC,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,uCAGd,GAAA;AACA,EAAOV,OAAAA,YAAAA;AAAA,IACL,yCAA0C,EAAA;AAAA,IAC1C,yCAA0C,EAAA;AAAA,GAC5C,CAAA;AACF,CAAA;AAcO,SAAS,8BAAA,CAKd,OACA,MAKA,EAAA;AAEA,EAAM,MAAA,cAAA,GAAiB,QAAQ,cAAkB,IAAA,sBAAA,CAAA;AAGjD,EAAA,MAAM,gBAAmB,GAAA;AAAA,IACvB,YAAY,EAAE,KAAA,EAAO,MAAM,UAAc,IAAA,IAAA,EAAM,YAAY,IAAK,EAAA;AAAA,IAChE,aAAa,EAAE,KAAA,EAAO,MAAM,WAAe,IAAA,IAAA,EAAM,YAAY,KAAM,EAAA;AAAA,GACrE,CAAA;AACA,EAAA,MAAM,QAAW,GAAA,gBAAA,CAAA;AAMjB,EAAM,MAAA,IAAA,GAAO,EAAE,GAAG,KAAM,EAAA,CAAA;AAExB,EAAM,MAAA,cAAA,GAAiB,qBAAsB,CAAA,cAA2B,CAAA,CAAA;AACxE,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,QAAU,EAAA;AAAA,MACR,cAAA,CAAe,SAAS,UAAU,CAAA;AAAA,MAClC,cAAA,CAAe,SAAS,WAAW,CAAA;AAAA,KACrC;AAAA,IACA,cAAA;AAAA,IACA,IAAA,EAAM,2CAA4C,CAAA,MAAA;AAAA,MAChD,IAAA;AAAA,KACF;AAAA,GACF,CAAA;AAMA,EAAO,OAAA,WAAA,CAAA;AACT,CAAA;AAcO,SAAS,iCAId,WAG4D,EAAA;AAC5D,EAAI,IAAA,WAAA,CAAY,QAAS,CAAA,MAAA,GAAS,CAAG,EAAA;AAEnC,IAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA,CAAA;AAAA,GACvC;AACA,EAAA,IAAI,YAAe,GAAA,CAAA,CAAA;AACnB,EAAA,MAAM,iBAAiB,MAAM;AAC3B,IAAM,MAAA,WAAA,GAAc,WAAY,CAAA,QAAA,CAAU,YAAY,CAAA,CAAA;AACtD,IAAgB,YAAA,IAAA,CAAA,CAAA;AAChB,IAAO,OAAA,WAAA,CAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,QAAU,EAAA;AAAA,MACR,YAAY,cAAe,EAAA;AAAA,MAC3B,aAAa,cAAe,EAAA;AAAA,KAC9B;AAAA,IACA,IAAM,EAAA,yCAAA,EAA4C,CAAA,MAAA,CAAO,YAAY,IAAI,CAAA;AAAA,GAC3E,CAAA;AACF,CAAA;AClLO,IAAM,oBAAuB,GAAA,EAAA;AAE7B,SAAS,2BAA8B,GAAA;AAC5C,EAAOH,OAAAA,aAAAA,EAAgB,CAAA,MAAA,CAAO,oBAAoB,CAAA,CAAA;AACpD,CAAA;AAyBO,SAAS,+BAAsE,GAAA;AACpF,EAAOS,OAAAA,gBAAAA;AAAA,IACLF,gBAAiB,CAAA;AAAA,MACf,CAAC,eAAiBP,EAAAA,aAAAA,EAAe,CAAA;AAAA,MACjC,CAAC,gBAAkBY,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,KACvC,CAAA;AAAA,IACD,CAAC,KAAW,MAAA,EAAE,GAAG,KAAA,EAAO,eAAe,oBAAqB,EAAA,CAAA;AAAA,GAC9D,CAAA;AACF,CAAA;AAEO,SAAS,+BAAkE,GAAA;AAChF,EAAA,OAAOJ,gBAAiB,CAAA;AAAA,IACtB,CAAC,eAAiBN,EAAAA,aAAAA,EAAe,CAAA;AAAA,IACjC,CAAC,gBAAkBW,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,GACvC,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,6BAGd,GAAA;AACA,EAAOV,OAAAA,YAAAA;AAAA,IACL,+BAAgC,EAAA;AAAA,IAChC,+BAAgC,EAAA;AAAA,GAClC,CAAA;AACF,CAAA;AAOO,SAAS,oBAAA,CAId,OACA,MACqD,EAAA;AAErD,EAAM,MAAA,cAAA,GAAiB,QAAQ,cAAkB,IAAA,sBAAA,CAAA;AAGjD,EAAA,MAAM,gBAAmB,GAAA;AAAA,IACvB,SAAS,EAAE,KAAA,EAAO,MAAM,OAAW,IAAA,IAAA,EAAM,YAAY,IAAK,EAAA;AAAA,GAC5D,CAAA;AACA,EAAA,MAAM,QAAW,GAAA,gBAAA,CAAA;AAMjB,EAAM,MAAA,IAAA,GAAO,EAAE,GAAG,KAAM,EAAA,CAAA;AAExB,EAAM,MAAA,cAAA,GAAiB,qBAAsB,CAAA,cAA2B,CAAA,CAAA;AACxE,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,QAAU,EAAA,CAAC,cAAe,CAAA,QAAA,CAAS,OAAO,CAAC,CAAA;AAAA,IAC3C,cAAA;AAAA,IACA,IAAA,EAAM,iCAAkC,CAAA,MAAA;AAAA,MACtC,IAAA;AAAA,KACF;AAAA,GACF,CAAA;AAEA,EAAO,OAAA,WAAA,CAAA;AACT,CAAA;AAaO,SAAS,uBAId,WAGkD,EAAA;AAClD,EAAI,IAAA,WAAA,CAAY,QAAS,CAAA,MAAA,GAAS,CAAG,EAAA;AAEnC,IAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA,CAAA;AAAA,GACvC;AACA,EAAA,IAAI,YAAe,GAAA,CAAA,CAAA;AACnB,EAAA,MAAM,iBAAiB,MAAM;AAC3B,IAAM,MAAA,WAAA,GAAc,WAAY,CAAA,QAAA,CAAU,YAAY,CAAA,CAAA;AACtD,IAAgB,YAAA,IAAA,CAAA,CAAA;AAChB,IAAO,OAAA,WAAA,CAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,QAAU,EAAA;AAAA,MACR,SAAS,cAAe,EAAA;AAAA,KAC1B;AAAA,IACA,IAAM,EAAA,+BAAA,EAAkC,CAAA,MAAA,CAAO,YAAY,IAAI,CAAA;AAAA,GACjE,CAAA;AACF,CAAA;AC5HO,IAAM,8BAAiC,GAAA,GAAA;AAEvC,SAAS,mCAAsC,GAAA;AACpD,EAAOH,OAAAA,aAAAA,EAAgB,CAAA,MAAA,CAAO,8BAA8B,CAAA,CAAA;AAC9D,CAAA;AAmCO,SAAS,uCAAsF,GAAA;AACpG,EAAOS,OAAAA,gBAAAA;AAAA,IACLF,gBAAiB,CAAA;AAAA,MACf,CAAC,eAAiBP,EAAAA,aAAAA,EAAe,CAAA;AAAA,MACjC,CAAC,MAAQY,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,MAC5B,CAAC,MAAQE,EAAAA,oBAAAA,CAAqBC,gBAAkBL,EAAAA,aAAAA,EAAe,CAAC,CAAA;AAAA,MAChE,CAAC,gBAAkBE,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,KACvC,CAAA;AAAA,IACD,CAAC,KAAW,MAAA,EAAE,GAAG,KAAA,EAAO,eAAe,8BAA+B,EAAA,CAAA;AAAA,GACxE,CAAA;AACF,CAAA;AAEO,SAAS,uCAAkF,GAAA;AAChG,EAAA,OAAOJ,gBAAiB,CAAA;AAAA,IACtB,CAAC,eAAiBN,EAAAA,aAAAA,EAAe,CAAA;AAAA,IACjC,CAAC,MAAQW,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,IAC5B,CAAC,MAAQG,EAAAA,oBAAAA,CAAqBC,gBAAkBN,EAAAA,aAAAA,EAAe,CAAC,CAAA;AAAA,IAChE,CAAC,gBAAkBE,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,GACvC,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,qCAGd,GAAA;AACA,EAAOV,OAAAA,YAAAA;AAAA,IACL,uCAAwC,EAAA;AAAA,IACxC,uCAAwC,EAAA;AAAA,GAC1C,CAAA;AACF,CAAA;AAaO,SAAS,4BAAA,CAKd,OACA,MAKA,EAAA;AAEA,EAAM,MAAA,cAAA,GAAiB,QAAQ,cAAkB,IAAA,sBAAA,CAAA;AAGjD,EAAA,MAAM,gBAAmB,GAAA;AAAA,IACvB,SAAS,EAAE,KAAA,EAAO,MAAM,OAAW,IAAA,IAAA,EAAM,YAAY,IAAK,EAAA;AAAA,IAC1D,aAAa,EAAE,KAAA,EAAO,MAAM,WAAe,IAAA,IAAA,EAAM,YAAY,KAAM,EAAA;AAAA,GACrE,CAAA;AACA,EAAA,MAAM,QAAW,GAAA,gBAAA,CAAA;AAMjB,EAAM,MAAA,IAAA,GAAO,EAAE,GAAG,KAAM,EAAA,CAAA;AAExB,EAAM,MAAA,cAAA,GAAiB,qBAAsB,CAAA,cAA2B,CAAA,CAAA;AACxE,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,QAAU,EAAA;AAAA,MACR,cAAA,CAAe,SAAS,OAAO,CAAA;AAAA,MAC/B,cAAA,CAAe,SAAS,WAAW,CAAA;AAAA,KACrC;AAAA,IACA,cAAA;AAAA,IACA,IAAA,EAAM,yCAA0C,CAAA,MAAA;AAAA,MAC9C,IAAA;AAAA,KACF;AAAA,GACF,CAAA;AAMA,EAAO,OAAA,WAAA,CAAA;AACT,CAAA;AAcO,SAAS,+BAId,WAG0D,EAAA;AAC1D,EAAI,IAAA,WAAA,CAAY,QAAS,CAAA,MAAA,GAAS,CAAG,EAAA;AAEnC,IAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA,CAAA;AAAA,GACvC;AACA,EAAA,IAAI,YAAe,GAAA,CAAA,CAAA;AACnB,EAAA,MAAM,iBAAiB,MAAM;AAC3B,IAAM,MAAA,WAAA,GAAc,WAAY,CAAA,QAAA,CAAU,YAAY,CAAA,CAAA;AACtD,IAAgB,YAAA,IAAA,CAAA,CAAA;AAChB,IAAO,OAAA,WAAA,CAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,QAAU,EAAA;AAAA,MACR,SAAS,cAAe,EAAA;AAAA,MACxB,aAAa,cAAe,EAAA;AAAA,KAC9B;AAAA,IACA,IAAM,EAAA,uCAAA,EAA0C,CAAA,MAAA,CAAO,YAAY,IAAI,CAAA;AAAA,GACzE,CAAA;AACF,CAAA;AC5KO,IAAM,qCAAwC,GAAA,EAAA;AAE9C,SAAS,0CAA6C,GAAA;AAC3D,EAAOH,OAAAA,aAAAA,EAAgB,CAAA,MAAA,CAAO,qCAAqC,CAAA,CAAA;AACrE,CAAA;AA+BO,SAAS,8CAAoG,GAAA;AAClH,EAAOS,OAAAA,gBAAAA;AAAA,IACLF,gBAAiB,CAAA;AAAA,MACf,CAAC,eAAiBP,EAAAA,aAAAA,EAAe,CAAA;AAAA,MACjC,CAAC,mBAAqBY,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,KAC1C,CAAA;AAAA,IACD,CAAC,KAAW,MAAA;AAAA,MACV,GAAG,KAAA;AAAA,MACH,aAAe,EAAA,qCAAA;AAAA,KACjB,CAAA;AAAA,GACF,CAAA;AACF,CAAA;AAEO,SAAS,8CAAgG,GAAA;AAC9G,EAAA,OAAOJ,gBAAiB,CAAA;AAAA,IACtB,CAAC,eAAiBN,EAAAA,aAAAA,EAAe,CAAA;AAAA,IACjC,CAAC,mBAAqBW,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,GAC1C,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,4CAGd,GAAA;AACA,EAAOV,OAAAA,YAAAA;AAAA,IACL,8CAA+C,EAAA;AAAA,IAC/C,8CAA+C,EAAA;AAAA,GACjD,CAAA;AACF,CAAA;AAWO,SAAS,mCAAA,CAKd,OAIA,MAKA,EAAA;AAEA,EAAM,MAAA,cAAA,GAAiB,QAAQ,cAAkB,IAAA,sBAAA,CAAA;AAGjD,EAAA,MAAM,gBAAmB,GAAA;AAAA,IACvB,cAAc,EAAE,KAAA,EAAO,MAAM,YAAgB,IAAA,IAAA,EAAM,YAAY,IAAK,EAAA;AAAA,IACpE,gBAAgB,EAAE,KAAA,EAAO,MAAM,cAAkB,IAAA,IAAA,EAAM,YAAY,KAAM,EAAA;AAAA,GAC3E,CAAA;AACA,EAAA,MAAM,QAAW,GAAA,gBAAA,CAAA;AAMjB,EAAM,MAAA,IAAA,GAAO,EAAE,GAAG,KAAM,EAAA,CAAA;AAExB,EAAM,MAAA,cAAA,GAAiB,qBAAsB,CAAA,cAA2B,CAAA,CAAA;AACxE,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,QAAU,EAAA;AAAA,MACR,cAAA,CAAe,SAAS,YAAY,CAAA;AAAA,MACpC,cAAA,CAAe,SAAS,cAAc,CAAA;AAAA,KACxC;AAAA,IACA,cAAA;AAAA,IACA,IAAA,EAAM,gDAAiD,CAAA,MAAA;AAAA,MACrD,IAAA;AAAA,KACF;AAAA,GACF,CAAA;AAMA,EAAO,OAAA,WAAA,CAAA;AACT,CAAA;AAcO,SAAS,sCAId,WAGiE,EAAA;AACjE,EAAI,IAAA,WAAA,CAAY,QAAS,CAAA,MAAA,GAAS,CAAG,EAAA;AAEnC,IAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA,CAAA;AAAA,GACvC;AACA,EAAA,IAAI,YAAe,GAAA,CAAA,CAAA;AACnB,EAAA,MAAM,iBAAiB,MAAM;AAC3B,IAAM,MAAA,WAAA,GAAc,WAAY,CAAA,QAAA,CAAU,YAAY,CAAA,CAAA;AACtD,IAAgB,YAAA,IAAA,CAAA,CAAA;AAChB,IAAO,OAAA,WAAA,CAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,QAAU,EAAA;AAAA,MACR,cAAc,cAAe,EAAA;AAAA,MAC7B,gBAAgB,cAAe,EAAA;AAAA,KACjC;AAAA,IACA,IAAA,EAAM,gDAAiD,CAAA,MAAA;AAAA,MACrD,WAAY,CAAA,IAAA;AAAA,KACd;AAAA,GACF,CAAA;AACF,CAAA;AC9JO,IAAM,4BAA+B,GAAA,EAAA;AAErC,SAAS,kCAAqC,GAAA;AACnD,EAAOH,OAAAA,aAAAA,EAAgB,CAAA,MAAA,CAAO,4BAA4B,CAAA,CAAA;AAC5D,CAAA;AAoCO,SAAS,sCAAoF,GAAA;AAClG,EAAOS,OAAAA,gBAAAA;AAAA,IACLF,gBAAiB,CAAA;AAAA,MACf,CAAC,eAAiBP,EAAAA,aAAAA,EAAe,CAAA;AAAA,MACjC,CAAC,UAAYU,EAAAA,aAAAA,EAAe,CAAA;AAAA,MAC5B,CAAC,OAASA,EAAAA,aAAAA,EAAe,CAAA;AAAA,MACzB,CAAC,gBAAkBE,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,KACvC,CAAA;AAAA,IACD,CAAC,KAAW,MAAA,EAAE,GAAG,KAAA,EAAO,eAAe,4BAA6B,EAAA,CAAA;AAAA,GACtE,CAAA;AACF,CAAA;AAEO,SAAS,sCAAgF,GAAA;AAC9F,EAAA,OAAOJ,gBAAiB,CAAA;AAAA,IACtB,CAAC,eAAiBN,EAAAA,aAAAA,EAAe,CAAA;AAAA,IACjC,CAAC,UAAYS,EAAAA,aAAAA,EAAe,CAAA;AAAA,IAC5B,CAAC,OAASA,EAAAA,aAAAA,EAAe,CAAA;AAAA,IACzB,CAAC,gBAAkBE,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,GACvC,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,oCAGd,GAAA;AACA,EAAOV,OAAAA,YAAAA;AAAA,IACL,sCAAuC,EAAA;AAAA,IACvC,sCAAuC,EAAA;AAAA,GACzC,CAAA;AACF,CAAA;AAaO,SAAS,2BAAA,CAKd,OACA,MAM0B,EAAA;AAE1B,EAAM,MAAA,cAAA,GAAiB,QAAQ,cAAkB,IAAA,sBAAA,CAAA;AAGjD,EAAA,MAAM,gBAAmB,GAAA;AAAA,IACvB,OAAO,EAAE,KAAA,EAAO,MAAM,KAAS,IAAA,IAAA,EAAM,YAAY,IAAK,EAAA;AAAA,IACtD,YAAY,EAAE,KAAA,EAAO,MAAM,UAAc,IAAA,IAAA,EAAM,YAAY,IAAK,EAAA;AAAA,GAClE,CAAA;AACA,EAAA,MAAM,QAAW,GAAA,gBAAA,CAAA;AAMjB,EAAM,MAAA,IAAA,GAAO,EAAE,GAAG,KAAM,EAAA,CAAA;AAGxB,EAAA,MAAM,YAAoB,CAAC,MAAA,CAAO,KAAK,KAAK,CAAA,GAAI,iBAAiB,CAAE,CAAA,MAAA;AAAA,IACjE,CAAC,CAAG,EAAA,CAAA,KAAM,CAAI,GAAA,CAAA;AAAA,IACd,CAAA;AAAA,GACF,CAAA;AAEA,EAAM,MAAA,cAAA,GAAiB,qBAAsB,CAAA,cAA2B,CAAA,CAAA;AACxE,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,QAAU,EAAA;AAAA,MACR,cAAA,CAAe,SAAS,KAAK,CAAA;AAAA,MAC7B,cAAA,CAAe,SAAS,UAAU,CAAA;AAAA,KACpC;AAAA,IACA,cAAA;AAAA,IACA,IAAA,EAAM,wCAAyC,CAAA,MAAA;AAAA,MAC7C,IAAA;AAAA,KACF;AAAA,GACF,CAAA;AAMA,EAAA,OAAO,OAAO,MAAO,CAAA,EAAE,GAAG,WAAA,EAAa,WAAW,CAAA,CAAA;AACpD,CAAA;AAcO,SAAS,8BAId,WAGyD,EAAA;AACzD,EAAI,IAAA,WAAA,CAAY,QAAS,CAAA,MAAA,GAAS,CAAG,EAAA;AAEnC,IAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA,CAAA;AAAA,GACvC;AACA,EAAA,IAAI,YAAe,GAAA,CAAA,CAAA;AACnB,EAAA,MAAM,iBAAiB,MAAM;AAC3B,IAAM,MAAA,WAAA,GAAc,WAAY,CAAA,QAAA,CAAU,YAAY,CAAA,CAAA;AACtD,IAAgB,YAAA,IAAA,CAAA,CAAA;AAChB,IAAO,OAAA,WAAA,CAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,QAAU,EAAA;AAAA,MACR,OAAO,cAAe,EAAA;AAAA,MACtB,YAAY,cAAe,EAAA;AAAA,KAC7B;AAAA,IACA,IAAM,EAAA,sCAAA,EAAyC,CAAA,MAAA,CAAO,YAAY,IAAI,CAAA;AAAA,GACxE,CAAA;AACF,CAAA;AC7KO,IAAM,sCAAyC,GAAA,EAAA;AAE/C,SAAS,0CAA6C,GAAA;AAC3D,EAAOH,OAAAA,aAAAA,EAAgB,CAAA,MAAA,CAAO,sCAAsC,CAAA,CAAA;AACtE,CAAA;AA4CO,SAAS,8CAAoG,GAAA;AAClH,EAAOS,OAAAA,gBAAAA;AAAA,IACLF,gBAAiB,CAAA;AAAA,MACf,CAAC,eAAiBP,EAAAA,aAAAA,EAAe,CAAA;AAAA,MACjC,CAAC,MAAQY,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,MAC5B,CAAC,MAAQE,EAAAA,oBAAAA,CAAqBC,gBAAkBL,EAAAA,aAAAA,EAAe,CAAC,CAAA;AAAA,MAChE,CAAC,QAAUA,EAAAA,aAAAA,EAAe,CAAA;AAAA,MAC1B,CAAC,OAASA,EAAAA,aAAAA,EAAe,CAAA;AAAA,MACzB,CAAC,gBAAkBE,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,KACvC,CAAA;AAAA,IACD,CAAC,KAAW,MAAA;AAAA,MACV,GAAG,KAAA;AAAA,MACH,aAAe,EAAA,sCAAA;AAAA,KACjB,CAAA;AAAA,GACF,CAAA;AACF,CAAA;AAEO,SAAS,8CAAgG,GAAA;AAC9G,EAAA,OAAOJ,gBAAiB,CAAA;AAAA,IACtB,CAAC,eAAiBN,EAAAA,aAAAA,EAAe,CAAA;AAAA,IACjC,CAAC,MAAQW,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,IAC5B,CAAC,MAAQG,EAAAA,oBAAAA,CAAqBC,gBAAkBN,EAAAA,aAAAA,EAAe,CAAC,CAAA;AAAA,IAChE,CAAC,QAAUA,EAAAA,aAAAA,EAAe,CAAA;AAAA,IAC1B,CAAC,OAASA,EAAAA,aAAAA,EAAe,CAAA;AAAA,IACzB,CAAC,gBAAkBE,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,GACvC,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,4CAGd,GAAA;AACA,EAAOV,OAAAA,YAAAA;AAAA,IACL,8CAA+C,EAAA;AAAA,IAC/C,8CAA+C,EAAA;AAAA,GACjD,CAAA;AACF,CAAA;AAiBO,SAAS,mCAAA,CAMd,OAKA,MAMA,EAAA;AAEA,EAAM,MAAA,cAAA,GAAiB,QAAQ,cAAkB,IAAA,sBAAA,CAAA;AAGjD,EAAA,MAAM,gBAAmB,GAAA;AAAA,IACvB,OAAO,EAAE,KAAA,EAAO,MAAM,KAAS,IAAA,IAAA,EAAM,YAAY,IAAK,EAAA;AAAA,IACtD,YAAY,EAAE,KAAA,EAAO,MAAM,UAAc,IAAA,IAAA,EAAM,YAAY,IAAK,EAAA;AAAA,IAChE,aAAa,EAAE,KAAA,EAAO,MAAM,WAAe,IAAA,IAAA,EAAM,YAAY,KAAM,EAAA;AAAA,GACrE,CAAA;AACA,EAAA,MAAM,QAAW,GAAA,gBAAA,CAAA;AAMjB,EAAM,MAAA,IAAA,GAAO,EAAE,GAAG,KAAM,EAAA,CAAA;AAExB,EAAM,MAAA,cAAA,GAAiB,qBAAsB,CAAA,cAA2B,CAAA,CAAA;AACxE,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,QAAU,EAAA;AAAA,MACR,cAAA,CAAe,SAAS,KAAK,CAAA;AAAA,MAC7B,cAAA,CAAe,SAAS,UAAU,CAAA;AAAA,MAClC,cAAA,CAAe,SAAS,WAAW,CAAA;AAAA,KACrC;AAAA,IACA,cAAA;AAAA,IACA,IAAA,EAAM,gDAAiD,CAAA,MAAA;AAAA,MACrD,IAAA;AAAA,KACF;AAAA,GACF,CAAA;AAOA,EAAO,OAAA,WAAA,CAAA;AACT,CAAA;AAeO,SAAS,sCAId,WAGiE,EAAA;AACjE,EAAI,IAAA,WAAA,CAAY,QAAS,CAAA,MAAA,GAAS,CAAG,EAAA;AAEnC,IAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA,CAAA;AAAA,GACvC;AACA,EAAA,IAAI,YAAe,GAAA,CAAA,CAAA;AACnB,EAAA,MAAM,iBAAiB,MAAM;AAC3B,IAAM,MAAA,WAAA,GAAc,WAAY,CAAA,QAAA,CAAU,YAAY,CAAA,CAAA;AACtD,IAAgB,YAAA,IAAA,CAAA,CAAA;AAChB,IAAO,OAAA,WAAA,CAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,QAAU,EAAA;AAAA,MACR,OAAO,cAAe,EAAA;AAAA,MACtB,YAAY,cAAe,EAAA;AAAA,MAC3B,aAAa,cAAe,EAAA;AAAA,KAC9B;AAAA,IACA,IAAA,EAAM,gDAAiD,CAAA,MAAA;AAAA,MACrD,WAAY,CAAA,IAAA;AAAA,KACd;AAAA,GACF,CAAA;AACF,CAAA;AChNO,IAAM,sCAAyC,GAAA,EAAA;AAE/C,SAAS,2CAA8C,GAAA;AAC5D,EAAOH,OAAAA,aAAAA,EAAgB,CAAA,MAAA,CAAO,sCAAsC,CAAA,CAAA;AACtE,CAAA;AAsCO,SAAS,+CAAsG,GAAA;AACpH,EAAOS,OAAAA,gBAAAA;AAAA,IACLF,gBAAiB,CAAA;AAAA,MACf,CAAC,eAAiBP,EAAAA,aAAAA,EAAe,CAAA;AAAA,MACjC,CAAC,gBAAkBY,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,KACvC,CAAA;AAAA,IACD,CAAC,KAAW,MAAA;AAAA,MACV,GAAG,KAAA;AAAA,MACH,aAAe,EAAA,sCAAA;AAAA,KACjB,CAAA;AAAA,GACF,CAAA;AACF,CAAA;AAEO,SAAS,+CAAkG,GAAA;AAChH,EAAA,OAAOJ,gBAAiB,CAAA;AAAA,IACtB,CAAC,eAAiBN,EAAAA,aAAAA,EAAe,CAAA;AAAA,IACjC,CAAC,gBAAkBW,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,GACvC,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,6CAGd,GAAA;AACA,EAAOV,OAAAA,YAAAA;AAAA,IACL,+CAAgD,EAAA;AAAA,IAChD,+CAAgD,EAAA;AAAA,GAClD,CAAA;AACF,CAAA;AAaO,SAAS,oCAAA,CAMd,OAKA,MAMA,EAAA;AAEA,EAAM,MAAA,cAAA,GAAiB,QAAQ,cAAkB,IAAA,sBAAA,CAAA;AAGjD,EAAA,MAAM,gBAAmB,GAAA;AAAA,IACvB,cAAc,EAAE,KAAA,EAAO,MAAM,YAAgB,IAAA,IAAA,EAAM,YAAY,IAAK,EAAA;AAAA,IACpE,uBAAyB,EAAA;AAAA,MACvB,KAAA,EAAO,MAAM,uBAA2B,IAAA,IAAA;AAAA,MACxC,UAAY,EAAA,KAAA;AAAA,KACd;AAAA,IACA,YAAY,EAAE,KAAA,EAAO,MAAM,UAAc,IAAA,IAAA,EAAM,YAAY,KAAM,EAAA;AAAA,GACnE,CAAA;AACA,EAAA,MAAM,QAAW,GAAA,gBAAA,CAAA;AAMjB,EAAM,MAAA,IAAA,GAAO,EAAE,GAAG,KAAM,EAAA,CAAA;AAGxB,EAAI,IAAA,CAAC,QAAS,CAAA,uBAAA,CAAwB,KAAO,EAAA;AAC3C,IAAA,QAAA,CAAS,wBAAwB,KAC/B,GAAA,6CAAA,CAAA;AAAA,GACJ;AACA,EAAI,IAAA,CAAC,QAAS,CAAA,UAAA,CAAW,KAAO,EAAA;AAC9B,IAAA,QAAA,CAAS,WAAW,KAClB,GAAA,6CAAA,CAAA;AAAA,GACJ;AAEA,EAAM,MAAA,cAAA,GAAiB,qBAAsB,CAAA,cAA2B,CAAA,CAAA;AACxE,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,QAAU,EAAA;AAAA,MACR,cAAA,CAAe,SAAS,YAAY,CAAA;AAAA,MACpC,cAAA,CAAe,SAAS,uBAAuB,CAAA;AAAA,MAC/C,cAAA,CAAe,SAAS,UAAU,CAAA;AAAA,KACpC;AAAA,IACA,cAAA;AAAA,IACA,IAAA,EAAM,iDAAkD,CAAA,MAAA;AAAA,MACtD,IAAA;AAAA,KACF;AAAA,GACF,CAAA;AAOA,EAAO,OAAA,WAAA,CAAA;AACT,CAAA;AAeO,SAAS,uCAId,WAGkE,EAAA;AAClE,EAAI,IAAA,WAAA,CAAY,QAAS,CAAA,MAAA,GAAS,CAAG,EAAA;AAEnC,IAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA,CAAA;AAAA,GACvC;AACA,EAAA,IAAI,YAAe,GAAA,CAAA,CAAA;AACnB,EAAA,MAAM,iBAAiB,MAAM;AAC3B,IAAM,MAAA,WAAA,GAAc,WAAY,CAAA,QAAA,CAAU,YAAY,CAAA,CAAA;AACtD,IAAgB,YAAA,IAAA,CAAA,CAAA;AAChB,IAAO,OAAA,WAAA,CAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,QAAU,EAAA;AAAA,MACR,cAAc,cAAe,EAAA;AAAA,MAC7B,yBAAyB,cAAe,EAAA;AAAA,MACxC,YAAY,cAAe,EAAA;AAAA,KAC7B;AAAA,IACA,IAAA,EAAM,iDAAkD,CAAA,MAAA;AAAA,MACtD,WAAY,CAAA,IAAA;AAAA,KACd;AAAA,GACF,CAAA;AACF,CAAA;AChMO,IAAM,0BAA6B,GAAA,EAAA;AAEnC,SAAS,gCAAmC,GAAA;AACjD,EAAOH,OAAAA,aAAAA,EAAgB,CAAA,MAAA,CAAO,0BAA0B,CAAA,CAAA;AAC1D,CAAA;AA6BO,SAAS,oCAAgF,GAAA;AAC9F,EAAOS,OAAAA,gBAAAA;AAAA,IACLF,gBAAiB,CAAA;AAAA,MACf,CAAC,eAAiBP,EAAAA,aAAAA,EAAe,CAAA;AAAA,MACjC,CAAC,QAAUU,EAAAA,aAAAA,EAAe,CAAA;AAAA,KAC3B,CAAA;AAAA,IACD,CAAC,KAAW,MAAA,EAAE,GAAG,KAAA,EAAO,eAAe,0BAA2B,EAAA,CAAA;AAAA,GACpE,CAAA;AACF,CAAA;AAEO,SAAS,oCAA4E,GAAA;AAC1F,EAAA,OAAOF,gBAAiB,CAAA;AAAA,IACtB,CAAC,eAAiBN,EAAAA,aAAAA,EAAe,CAAA;AAAA,IACjC,CAAC,QAAUS,EAAAA,aAAAA,EAAe,CAAA;AAAA,GAC3B,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,kCAGd,GAAA;AACA,EAAOR,OAAAA,YAAAA;AAAA,IACL,oCAAqC,EAAA;AAAA,IACrC,oCAAqC,EAAA;AAAA,GACvC,CAAA;AACF,CAAA;AAWO,SAAS,yBAAA,CAKd,OACA,MAKA,EAAA;AAEA,EAAM,MAAA,cAAA,GAAiB,QAAQ,cAAkB,IAAA,sBAAA,CAAA;AAGjD,EAAA,MAAM,gBAAmB,GAAA;AAAA,IACvB,QAAQ,EAAE,KAAA,EAAO,MAAM,MAAU,IAAA,IAAA,EAAM,YAAY,IAAK,EAAA;AAAA,IACxD,aAAa,EAAE,KAAA,EAAO,MAAM,WAAe,IAAA,IAAA,EAAM,YAAY,IAAK,EAAA;AAAA,GACpE,CAAA;AACA,EAAA,MAAM,QAAW,GAAA,gBAAA,CAAA;AAMjB,EAAM,MAAA,IAAA,GAAO,EAAE,GAAG,KAAM,EAAA,CAAA;AAExB,EAAM,MAAA,cAAA,GAAiB,qBAAsB,CAAA,cAA2B,CAAA,CAAA;AACxE,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,QAAU,EAAA;AAAA,MACR,cAAA,CAAe,SAAS,MAAM,CAAA;AAAA,MAC9B,cAAA,CAAe,SAAS,WAAW,CAAA;AAAA,KACrC;AAAA,IACA,cAAA;AAAA,IACA,IAAA,EAAM,sCAAuC,CAAA,MAAA;AAAA,MAC3C,IAAA;AAAA,KACF;AAAA,GACF,CAAA;AAMA,EAAO,OAAA,WAAA,CAAA;AACT,CAAA;AAcO,SAAS,4BAId,WAGuD,EAAA;AACvD,EAAI,IAAA,WAAA,CAAY,QAAS,CAAA,MAAA,GAAS,CAAG,EAAA;AAEnC,IAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA,CAAA;AAAA,GACvC;AACA,EAAA,IAAI,YAAe,GAAA,CAAA,CAAA;AACnB,EAAA,MAAM,iBAAiB,MAAM;AAC3B,IAAM,MAAA,WAAA,GAAc,WAAY,CAAA,QAAA,CAAU,YAAY,CAAA,CAAA;AACtD,IAAgB,YAAA,IAAA,CAAA,CAAA;AAChB,IAAO,OAAA,WAAA,CAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,QAAU,EAAA;AAAA,MACR,QAAQ,cAAe,EAAA;AAAA,MACvB,aAAa,cAAe,EAAA;AAAA,KAC9B;AAAA,IACA,IAAM,EAAA,oCAAA,EAAuC,CAAA,MAAA,CAAO,YAAY,IAAI,CAAA;AAAA,GACtE,CAAA;AACF,CAAA;ACpJO,IAAM,oCAAuC,GAAA,GAAA;AAE7C,SAAS,wCAA2C,GAAA;AACzD,EAAOH,OAAAA,aAAAA,EAAgB,CAAA,MAAA,CAAO,oCAAoC,CAAA,CAAA;AACpE,CAAA;AAuCO,SAAS,4CAAgG,GAAA;AAC9G,EAAOS,OAAAA,gBAAAA;AAAA,IACLF,gBAAiB,CAAA;AAAA,MACf,CAAC,eAAiBP,EAAAA,aAAAA,EAAe,CAAA;AAAA,MACjC,CAAC,QAAUU,EAAAA,aAAAA,EAAe,CAAA;AAAA,MAC1B,CAAC,UAAYI,EAAAA,oBAAAA,CAAqBC,gBAAkBL,EAAAA,aAAAA,EAAe,CAAC,CAAA;AAAA,MACpE,CAAC,WAAaE,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,KAClC,CAAA;AAAA,IACD,CAAC,KAAW,MAAA;AAAA,MACV,GAAG,KAAA;AAAA,MACH,aAAe,EAAA,oCAAA;AAAA,KACjB,CAAA;AAAA,GACF,CAAA;AACF,CAAA;AAEO,SAAS,4CAA4F,GAAA;AAC1G,EAAA,OAAOJ,gBAAiB,CAAA;AAAA,IACtB,CAAC,eAAiBN,EAAAA,aAAAA,EAAe,CAAA;AAAA,IACjC,CAAC,QAAUS,EAAAA,aAAAA,EAAe,CAAA;AAAA,IAC1B,CAAC,UAAYK,EAAAA,oBAAAA,CAAqBC,gBAAkBN,EAAAA,aAAAA,EAAe,CAAC,CAAA;AAAA,IACpE,CAAC,WAAaE,EAAAA,iBAAAA,EAAmB,CAAA;AAAA,GAClC,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,0CAGd,GAAA;AACA,EAAOV,OAAAA,YAAAA;AAAA,IACL,4CAA6C,EAAA;AAAA,IAC7C,4CAA6C,EAAA;AAAA,GAC/C,CAAA;AACF,CAAA;AAeO,SAAS,iCAAA,CAMd,OAKA,MAMA,EAAA;AAEA,EAAM,MAAA,cAAA,GAAiB,QAAQ,cAAkB,IAAA,sBAAA,CAAA;AAGjD,EAAA,MAAM,gBAAmB,GAAA;AAAA,IACvB,QAAQ,EAAE,KAAA,EAAO,MAAM,MAAU,IAAA,IAAA,EAAM,YAAY,IAAK,EAAA;AAAA,IACxD,aAAa,EAAE,KAAA,EAAO,MAAM,WAAe,IAAA,IAAA,EAAM,YAAY,KAAM,EAAA;AAAA,IACnE,aAAa,EAAE,KAAA,EAAO,MAAM,WAAe,IAAA,IAAA,EAAM,YAAY,IAAK,EAAA;AAAA,GACpE,CAAA;AACA,EAAA,MAAM,QAAW,GAAA,gBAAA,CAAA;AAMjB,EAAM,MAAA,IAAA,GAAO,EAAE,GAAG,KAAM,EAAA,CAAA;AAExB,EAAM,MAAA,cAAA,GAAiB,qBAAsB,CAAA,cAA2B,CAAA,CAAA;AACxE,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,QAAU,EAAA;AAAA,MACR,cAAA,CAAe,SAAS,MAAM,CAAA;AAAA,MAC9B,cAAA,CAAe,SAAS,WAAW,CAAA;AAAA,MACnC,cAAA,CAAe,SAAS,WAAW,CAAA;AAAA,KACrC;AAAA,IACA,cAAA;AAAA,IACA,IAAA,EAAM,8CAA+C,CAAA,MAAA;AAAA,MACnD,IAAA;AAAA,KACF;AAAA,GACF,CAAA;AAOA,EAAO,OAAA,WAAA,CAAA;AACT,CAAA;AAeO,SAAS,oCAId,WAG+D,EAAA;AAC/D,EAAI,IAAA,WAAA,CAAY,QAAS,CAAA,MAAA,GAAS,CAAG,EAAA;AAEnC,IAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA,CAAA;AAAA,GACvC;AACA,EAAA,IAAI,YAAe,GAAA,CAAA,CAAA;AACnB,EAAA,MAAM,iBAAiB,MAAM;AAC3B,IAAM,MAAA,WAAA,GAAc,WAAY,CAAA,QAAA,CAAU,YAAY,CAAA,CAAA;AACtD,IAAgB,YAAA,IAAA,CAAA,CAAA;AAChB,IAAO,OAAA,WAAA,CAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,QAAU,EAAA;AAAA,MACR,QAAQ,cAAe,EAAA;AAAA,MACvB,aAAa,cAAe,EAAA;AAAA,MAC5B,aAAa,cAAe,EAAA;AAAA,KAC9B;AAAA,IACA,IAAA,EAAM,8CAA+C,CAAA,MAAA;AAAA,MACnD,WAAY,CAAA,IAAA;AAAA,KACd;AAAA,GACF,CAAA;AACF,CAAA;ACvMO,IAAM,mCAAsC,GAAA,GAAA;AAE5C,SAAS,wCAA2C,GAAA;AACzD,EAAOH,OAAAA,aAAAA,EAAgB,CAAA,MAAA,CAAO,mCAAmC,CAAA,CAAA;AACnE,CAAA;AAqBO,SAAS,4CAAgG,GAAA;AAC9G,EAAOS,OAAAA,gBAAAA;AAAA,IACLF,iBAAiB,CAAC,CAAC,iBAAiBP,aAAc,EAAC,CAAC,CAAC,CAAA;AAAA,IACrD,CAAC,KAAW,MAAA;AAAA,MACV,GAAG,KAAA;AAAA,MACH,aAAe,EAAA,mCAAA;AAAA,KACjB,CAAA;AAAA,GACF,CAAA;AACF,CAAA;AAEO,SAAS,4CAA4F,GAAA;AAC1G,EAAA,OAAOQ,iBAAiB,CAAC,CAAC,iBAAiBN,aAAc,EAAC,CAAC,CAAC,CAAA,CAAA;AAC9D,CAAA;AAEO,SAAS,0CAGd,GAAA;AACA,EAAOC,OAAAA,YAAAA;AAAA,IACL,4CAA6C,EAAA;AAAA,IAC7C,4CAA6C,EAAA;AAAA,GAC/C,CAAA;AACF,CAAA;AAQO,SAAS,iCAAA,CAId,OACA,MACuE,EAAA;AAEvE,EAAM,MAAA,cAAA,GAAiB,QAAQ,cAAkB,IAAA,sBAAA,CAAA;AAGjD,EAAA,MAAM,gBAAmB,GAAA;AAAA,IACvB,cAAc,EAAE,KAAA,EAAO,MAAM,YAAgB,IAAA,IAAA,EAAM,YAAY,IAAK,EAAA;AAAA,GACtE,CAAA;AACA,EAAA,MAAM,QAAW,GAAA,gBAAA,CAAA;AAKjB,EAAM,MAAA,cAAA,GAAiB,qBAAsB,CAAA,cAA2B,CAAA,CAAA;AACxE,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,QAAU,EAAA,CAAC,cAAe,CAAA,QAAA,CAAS,YAAY,CAAC,CAAA;AAAA,IAChD,cAAA;AAAA,IACA,IAAM,EAAA,4CAAA,EAA+C,CAAA,MAAA,CAAO,EAAE,CAAA;AAAA,GAChE,CAAA;AAEA,EAAO,OAAA,WAAA,CAAA;AACT,CAAA;AAaO,SAAS,oCAId,WAG+D,EAAA;AAC/D,EAAI,IAAA,WAAA,CAAY,QAAS,CAAA,MAAA,GAAS,CAAG,EAAA;AAEnC,IAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA,CAAA;AAAA,GACvC;AACA,EAAA,IAAI,YAAe,GAAA,CAAA,CAAA;AACnB,EAAA,MAAM,iBAAiB,MAAM;AAC3B,IAAM,MAAA,WAAA,GAAc,WAAY,CAAA,QAAA,CAAU,YAAY,CAAA,CAAA;AACtD,IAAgB,YAAA,IAAA,CAAA,CAAA;AAChB,IAAO,OAAA,WAAA,CAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,QAAU,EAAA;AAAA,MACR,cAAc,cAAe,EAAA;AAAA,KAC/B;AAAA,IACA,IAAA,EAAM,8CAA+C,CAAA,MAAA;AAAA,MACnD,WAAY,CAAA,IAAA;AAAA,KACd;AAAA,GACF,CAAA;AACF,CAAA;ACpHO,IAAM,oCAAuC,GAAA,EAAA;AAE7C,SAAS,yCAA4C,GAAA;AAC1D,EAAOH,OAAAA,aAAAA,EAAgB,CAAA,MAAA,CAAO,oCAAoC,CAAA,CAAA;AACpE,CAAA;AA+CO,SAAS,6CAAkG,GAAA;AAChH,EAAOS,OAAAA,gBAAAA;AAAA,IACLF,gBAAiB,CAAA;AAAA,MACf,CAAC,eAAiBP,EAAAA,aAAAA,EAAe,CAAA;AAAA,MACjC,CAAC,gBAAkBU,EAAAA,aAAAA,EAAe,CAAA;AAAA,KACnC,CAAA;AAAA,IACD,CAAC,KAAW,MAAA;AAAA,MACV,GAAG,KAAA;AAAA,MACH,aAAe,EAAA,oCAAA;AAAA,KACjB,CAAA;AAAA,GACF,CAAA;AACF,CAAA;AAEO,SAAS,6CAA8F,GAAA;AAC5G,EAAA,OAAOF,gBAAiB,CAAA;AAAA,IACtB,CAAC,eAAiBN,EAAAA,aAAAA,EAAe,CAAA;AAAA,IACjC,CAAC,gBAAkBS,EAAAA,aAAAA,EAAe,CAAA;AAAA,GACnC,CAAA,CAAA;AACH,CAAA;AAEO,SAAS,2CAGd,GAAA;AACA,EAAOR,OAAAA,YAAAA;AAAA,IACL,6CAA8C,EAAA;AAAA,IAC9C,6CAA8C,EAAA;AAAA,GAChD,CAAA;AACF,CAAA;AAiBO,SAAS,kCAAA,CAQd,OAOA,MAQA,EAAA;AAEA,EAAM,MAAA,cAAA,GAAiB,QAAQ,cAAkB,IAAA,sBAAA,CAAA;AAGjD,EAAA,MAAM,gBAAmB,GAAA;AAAA,IACvB,cAAc,EAAE,KAAA,EAAO,MAAM,YAAgB,IAAA,IAAA,EAAM,YAAY,IAAK,EAAA;AAAA,IACpE,gBAAkB,EAAA;AAAA,MAChB,KAAA,EAAO,MAAM,gBAAoB,IAAA,IAAA;AAAA,MACjC,UAAY,EAAA,IAAA;AAAA,KACd;AAAA,IACA,uBAAyB,EAAA;AAAA,MACvB,KAAA,EAAO,MAAM,uBAA2B,IAAA,IAAA;AAAA,MACxC,UAAY,EAAA,KAAA;AAAA,KACd;AAAA,IACA,YAAY,EAAE,KAAA,EAAO,MAAM,UAAc,IAAA,IAAA,EAAM,YAAY,KAAM,EAAA;AAAA,IACjE,gBAAgB,EAAE,KAAA,EAAO,MAAM,cAAkB,IAAA,IAAA,EAAM,YAAY,KAAM,EAAA;AAAA,GAC3E,CAAA;AACA,EAAA,MAAM,QAAW,GAAA,gBAAA,CAAA;AAMjB,EAAM,MAAA,IAAA,GAAO,EAAE,GAAG,KAAM,EAAA,CAAA;AAGxB,EAAI,IAAA,CAAC,QAAS,CAAA,uBAAA,CAAwB,KAAO,EAAA;AAC3C,IAAA,QAAA,CAAS,wBAAwB,KAC/B,GAAA,6CAAA,CAAA;AAAA,GACJ;AACA,EAAI,IAAA,CAAC,QAAS,CAAA,UAAA,CAAW,KAAO,EAAA;AAC9B,IAAA,QAAA,CAAS,WAAW,KAClB,GAAA,6CAAA,CAAA;AAAA,GACJ;AAEA,EAAM,MAAA,cAAA,GAAiB,qBAAsB,CAAA,cAA2B,CAAA,CAAA;AACxE,EAAA,MAAM,WAAc,GAAA;AAAA,IAClB,QAAU,EAAA;AAAA,MACR,cAAA,CAAe,SAAS,YAAY,CAAA;AAAA,MACpC,cAAA,CAAe,SAAS,gBAAgB,CAAA;AAAA,MACxC,cAAA,CAAe,SAAS,uBAAuB,CAAA;AAAA,MAC/C,cAAA,CAAe,SAAS,UAAU,CAAA;AAAA,MAClC,cAAA,CAAe,SAAS,cAAc,CAAA;AAAA,KACxC;AAAA,IACA,cAAA;AAAA,IACA,IAAA,EAAM,+CAAgD,CAAA,MAAA;AAAA,MACpD,IAAA;AAAA,KACF;AAAA,GACF,CAAA;AASA,EAAO,OAAA,WAAA,CAAA;AACT,CAAA;AAiBO,SAAS,qCAId,WAGgE,EAAA;AAChE,EAAI,IAAA,WAAA,CAAY,QAAS,CAAA,MAAA,GAAS,CAAG,EAAA;AAEnC,IAAM,MAAA,IAAI,MAAM,qBAAqB,CAAA,CAAA;AAAA,GACvC;AACA,EAAA,IAAI,YAAe,GAAA,CAAA,CAAA;AACnB,EAAA,MAAM,iBAAiB,MAAM;AAC3B,IAAM,MAAA,WAAA,GAAc,WAAY,CAAA,QAAA,CAAU,YAAY,CAAA,CAAA;AACtD,IAAgB,YAAA,IAAA,CAAA,CAAA;AAChB,IAAO,OAAA,WAAA,CAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA;AAAA,IACL,gBAAgB,WAAY,CAAA,cAAA;AAAA,IAC5B,QAAU,EAAA;AAAA,MACR,cAAc,cAAe,EAAA;AAAA,MAC7B,kBAAkB,cAAe,EAAA;AAAA,MACjC,yBAAyB,cAAe,EAAA;AAAA,MACxC,YAAY,cAAe,EAAA;AAAA,MAC3B,gBAAgB,cAAe,EAAA;AAAA,KACjC;AAAA,IACA,IAAA,EAAM,+CAAgD,CAAA,MAAA;AAAA,MACpD,WAAY,CAAA,IAAA;AAAA,KACd;AAAA,GACF,CAAA;AACF", "file": "index.mjs", "sourcesContent": ["/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  combineCodec,\n  getEnumDecoder,\n  getEnumEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  type Codec,\n  type Decoder,\n  type Encoder,\n} from '@solana/kit';\n\nexport enum NonceState {\n  Uninitialized,\n  Initialized,\n}\n\nexport type NonceStateArgs = NonceState;\n\nexport function getNonceStateEncoder(): Encoder<NonceStateArgs> {\n  return getEnumEncoder(NonceState, { size: getU32Encoder() });\n}\n\nexport function getNonceStateDecoder(): Decoder<NonceState> {\n  return getEnumDecoder(NonceState, { size: getU32Decoder() });\n}\n\nexport function getNonceStateCodec(): Codec<NonceStateArgs, NonceState> {\n  return combineCodec(getNonceStateEncoder(), getNonceStateDecoder());\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  combineCodec,\n  getEnumDecoder,\n  getEnumEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  type Codec,\n  type Decoder,\n  type Encoder,\n} from '@solana/kit';\n\nexport enum NonceVersion {\n  Legacy,\n  Current,\n}\n\nexport type NonceVersionArgs = NonceVersion;\n\nexport function getNonceVersionEncoder(): Encoder<NonceVersionArgs> {\n  return getEnumEncoder(NonceVersion, { size: getU32Encoder() });\n}\n\nexport function getNonceVersionDecoder(): Decoder<NonceVersion> {\n  return getEnumDecoder(NonceVersion, { size: getU32Decoder() });\n}\n\nexport function getNonceVersionCodec(): Codec<NonceVersionArgs, NonceVersion> {\n  return combineCodec(getNonceVersionEncoder(), getNonceVersionDecoder());\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  assertAccountExists,\n  assertAccountsExist,\n  combineCodec,\n  decodeAccount,\n  fetchEncodedAccount,\n  fetchEncodedAccounts,\n  getAddressDecoder,\n  getAddressEncoder,\n  getStructDecoder,\n  getStructEncoder,\n  getU64Decoder,\n  getU64Encoder,\n  type Account,\n  type Address,\n  type Codec,\n  type Decoder,\n  type EncodedAccount,\n  type Encoder,\n  type FetchAccountConfig,\n  type FetchAccountsConfig,\n  type MaybeAccount,\n  type MaybeEncodedAccount,\n} from '@solana/kit';\nimport {\n  getNonceStateDecoder,\n  getNonceStateEncoder,\n  getNonceVersionDecoder,\n  getNonceVersionEncoder,\n  type NonceState,\n  type NonceStateArgs,\n  type NonceVersion,\n  type NonceVersionArgs,\n} from '../types';\n\nexport type Nonce = {\n  version: NonceVersion;\n  state: NonceState;\n  authority: Address;\n  blockhash: Address;\n  lamportsPerSignature: bigint;\n};\n\nexport type NonceArgs = {\n  version: NonceVersionArgs;\n  state: NonceStateArgs;\n  authority: Address;\n  blockhash: Address;\n  lamportsPerSignature: number | bigint;\n};\n\nexport function getNonceEncoder(): Encoder<NonceArgs> {\n  return getStructEncoder([\n    ['version', getNonceVersionEncoder()],\n    ['state', getNonceStateEncoder()],\n    ['authority', getAddressEncoder()],\n    ['blockhash', getAddressEncoder()],\n    ['lamportsPerSignature', getU64Encoder()],\n  ]);\n}\n\nexport function getNonceDecoder(): Decoder<Nonce> {\n  return getStructDecoder([\n    ['version', getNonceVersionDecoder()],\n    ['state', getNonceStateDecoder()],\n    ['authority', getAddressDecoder()],\n    ['blockhash', getAddressDecoder()],\n    ['lamportsPerSignature', getU64Decoder()],\n  ]);\n}\n\nexport function getNonceCodec(): Codec<NonceArgs, Nonce> {\n  return combineCodec(getNonceEncoder(), getNonceDecoder());\n}\n\nexport function decodeNonce<TAddress extends string = string>(\n  encodedAccount: EncodedAccount<TAddress>\n): Account<Nonce, TAddress>;\nexport function decodeNonce<TAddress extends string = string>(\n  encodedAccount: MaybeEncodedAccount<TAddress>\n): MaybeAccount<Nonce, TAddress>;\nexport function decodeNonce<TAddress extends string = string>(\n  encodedAccount: EncodedAccount<TAddress> | MaybeEncodedAccount<TAddress>\n): Account<Nonce, TAddress> | MaybeAccount<Nonce, TAddress> {\n  return decodeAccount(\n    encodedAccount as MaybeEncodedAccount<TAddress>,\n    getNonceDecoder()\n  );\n}\n\nexport async function fetchNonce<TAddress extends string = string>(\n  rpc: Parameters<typeof fetchEncodedAccount>[0],\n  address: Address<TAddress>,\n  config?: FetchAccountConfig\n): Promise<Account<Nonce, TAddress>> {\n  const maybeAccount = await fetchMaybeNonce(rpc, address, config);\n  assertAccountExists(maybeAccount);\n  return maybeAccount;\n}\n\nexport async function fetchMaybeNonce<TAddress extends string = string>(\n  rpc: Parameters<typeof fetchEncodedAccount>[0],\n  address: Address<TAddress>,\n  config?: FetchAccountConfig\n): Promise<MaybeAccount<Nonce, TAddress>> {\n  const maybeAccount = await fetchEncodedAccount(rpc, address, config);\n  return decodeNonce(maybeAccount);\n}\n\nexport async function fetchAllNonce(\n  rpc: Parameters<typeof fetchEncodedAccounts>[0],\n  addresses: Array<Address>,\n  config?: FetchAccountsConfig\n): Promise<Account<Nonce>[]> {\n  const maybeAccounts = await fetchAllMaybeNonce(rpc, addresses, config);\n  assertAccountsExist(maybeAccounts);\n  return maybeAccounts;\n}\n\nexport async function fetchAllMaybeNonce(\n  rpc: Parameters<typeof fetchEncodedAccounts>[0],\n  addresses: Array<Address>,\n  config?: FetchAccountsConfig\n): Promise<MaybeAccount<Nonce>[]> {\n  const maybeAccounts = await fetchEncodedAccounts(rpc, addresses, config);\n  return maybeAccounts.map((maybeAccount) => decodeNonce(maybeAccount));\n}\n\nexport function getNonceSize(): number {\n  return 80;\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  containsBytes,\n  getU32Encoder,\n  type Address,\n  type ReadonlyUint8Array,\n} from '@solana/kit';\nimport {\n  type ParsedAdvanceNonceAccountInstruction,\n  type ParsedAllocateInstruction,\n  type ParsedAllocateWithSeedInstruction,\n  type ParsedAssignInstruction,\n  type ParsedAssignWithSeedInstruction,\n  type ParsedAuthorizeNonceAccountInstruction,\n  type ParsedCreateAccountInstruction,\n  type ParsedCreateAccountWithSeedInstruction,\n  type ParsedInitializeNonceAccountInstruction,\n  type ParsedTransferSolInstruction,\n  type ParsedTransferSolWithSeedInstruction,\n  type ParsedUpgradeNonceAccountInstruction,\n  type ParsedWithdrawNonceAccountInstruction,\n} from '../instructions';\n\nexport const SYSTEM_PROGRAM_ADDRESS =\n  '11111111111111111111111111111111' as Address<'11111111111111111111111111111111'>;\n\nexport enum SystemAccount {\n  Nonce,\n}\n\nexport enum SystemInstruction {\n  CreateAccount,\n  Assign,\n  TransferSol,\n  CreateAccountWithSeed,\n  AdvanceNonceAccount,\n  WithdrawNonceAccount,\n  InitializeNonceAccount,\n  AuthorizeNonceAccount,\n  Allocate,\n  AllocateWithSeed,\n  AssignWithSeed,\n  TransferSolWithSeed,\n  UpgradeNonceAccount,\n}\n\nexport function identifySystemInstruction(\n  instruction: { data: ReadonlyUint8Array } | ReadonlyUint8Array\n): SystemInstruction {\n  const data = 'data' in instruction ? instruction.data : instruction;\n  if (containsBytes(data, getU32Encoder().encode(0), 0)) {\n    return SystemInstruction.CreateAccount;\n  }\n  if (containsBytes(data, getU32Encoder().encode(1), 0)) {\n    return SystemInstruction.Assign;\n  }\n  if (containsBytes(data, getU32Encoder().encode(2), 0)) {\n    return SystemInstruction.TransferSol;\n  }\n  if (containsBytes(data, getU32Encoder().encode(3), 0)) {\n    return SystemInstruction.CreateAccountWithSeed;\n  }\n  if (containsBytes(data, getU32Encoder().encode(4), 0)) {\n    return SystemInstruction.AdvanceNonceAccount;\n  }\n  if (containsBytes(data, getU32Encoder().encode(5), 0)) {\n    return SystemInstruction.WithdrawNonceAccount;\n  }\n  if (containsBytes(data, getU32Encoder().encode(6), 0)) {\n    return SystemInstruction.InitializeNonceAccount;\n  }\n  if (containsBytes(data, getU32Encoder().encode(7), 0)) {\n    return SystemInstruction.AuthorizeNonceAccount;\n  }\n  if (containsBytes(data, getU32Encoder().encode(8), 0)) {\n    return SystemInstruction.Allocate;\n  }\n  if (containsBytes(data, getU32Encoder().encode(9), 0)) {\n    return SystemInstruction.AllocateWithSeed;\n  }\n  if (containsBytes(data, getU32Encoder().encode(10), 0)) {\n    return SystemInstruction.AssignWithSeed;\n  }\n  if (containsBytes(data, getU32Encoder().encode(11), 0)) {\n    return SystemInstruction.TransferSolWithSeed;\n  }\n  if (containsBytes(data, getU32Encoder().encode(12), 0)) {\n    return SystemInstruction.UpgradeNonceAccount;\n  }\n  throw new Error(\n    'The provided instruction could not be identified as a system instruction.'\n  );\n}\n\nexport type ParsedSystemInstruction<\n  TProgram extends string = '11111111111111111111111111111111',\n> =\n  | ({\n      instructionType: SystemInstruction.CreateAccount;\n    } & ParsedCreateAccountInstruction<TProgram>)\n  | ({\n      instructionType: SystemInstruction.Assign;\n    } & ParsedAssignInstruction<TProgram>)\n  | ({\n      instructionType: SystemInstruction.TransferSol;\n    } & ParsedTransferSolInstruction<TProgram>)\n  | ({\n      instructionType: SystemInstruction.CreateAccountWithSeed;\n    } & ParsedCreateAccountWithSeedInstruction<TProgram>)\n  | ({\n      instructionType: SystemInstruction.AdvanceNonceAccount;\n    } & ParsedAdvanceNonceAccountInstruction<TProgram>)\n  | ({\n      instructionType: SystemInstruction.WithdrawNonceAccount;\n    } & ParsedWithdrawNonceAccountInstruction<TProgram>)\n  | ({\n      instructionType: SystemInstruction.InitializeNonceAccount;\n    } & ParsedInitializeNonceAccountInstruction<TProgram>)\n  | ({\n      instructionType: SystemInstruction.AuthorizeNonceAccount;\n    } & ParsedAuthorizeNonceAccountInstruction<TProgram>)\n  | ({\n      instructionType: SystemInstruction.Allocate;\n    } & ParsedAllocateInstruction<TProgram>)\n  | ({\n      instructionType: SystemInstruction.AllocateWithSeed;\n    } & ParsedAllocateWithSeedInstruction<TProgram>)\n  | ({\n      instructionType: SystemInstruction.AssignWithSeed;\n    } & ParsedAssignWithSeedInstruction<TProgram>)\n  | ({\n      instructionType: SystemInstruction.TransferSolWithSeed;\n    } & ParsedTransferSolWithSeedInstruction<TProgram>)\n  | ({\n      instructionType: SystemInstruction.UpgradeNonceAccount;\n    } & ParsedUpgradeNonceAccountInstruction<TProgram>);\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  isProgramError,\n  type Address,\n  type SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM,\n  type SolanaError,\n} from '@solana/kit';\nimport { SYSTEM_PROGRAM_ADDRESS } from '../programs';\n\n/** AccountAlreadyInUse: an account with the same address already exists */\nexport const SYSTEM_ERROR__ACCOUNT_ALREADY_IN_USE = 0x0; // 0\n/** ResultWithNegativeLamports: account does not have enough SOL to perform the operation */\nexport const SYSTEM_ERROR__RESULT_WITH_NEGATIVE_LAMPORTS = 0x1; // 1\n/** InvalidProgramId: cannot assign account to this program id */\nexport const SYSTEM_ERROR__INVALID_PROGRAM_ID = 0x2; // 2\n/** InvalidAccountDataLength: cannot allocate account data of this length */\nexport const SYSTEM_ERROR__INVALID_ACCOUNT_DATA_LENGTH = 0x3; // 3\n/** MaxSeedLengthExceeded: length of requested seed is too long */\nexport const SYSTEM_ERROR__MAX_SEED_LENGTH_EXCEEDED = 0x4; // 4\n/** AddressWithSeedMismatch: provided address does not match addressed derived from seed */\nexport const SYSTEM_ERROR__ADDRESS_WITH_SEED_MISMATCH = 0x5; // 5\n/** NonceNoRecentBlockhashes: advancing stored nonce requires a populated RecentBlockhashes sysvar */\nexport const SYSTEM_ERROR__NONCE_NO_RECENT_BLOCKHASHES = 0x6; // 6\n/** NonceBlockhashNotExpired: stored nonce is still in recent_blockhashes */\nexport const SYSTEM_ERROR__NONCE_BLOCKHASH_NOT_EXPIRED = 0x7; // 7\n/** NonceUnexpectedBlockhashValue: specified nonce does not match stored nonce */\nexport const SYSTEM_ERROR__NONCE_UNEXPECTED_BLOCKHASH_VALUE = 0x8; // 8\n\nexport type SystemError =\n  | typeof SYSTEM_ERROR__ACCOUNT_ALREADY_IN_USE\n  | typeof SYSTEM_ERROR__ADDRESS_WITH_SEED_MISMATCH\n  | typeof SYSTEM_ERROR__INVALID_ACCOUNT_DATA_LENGTH\n  | typeof SYSTEM_ERROR__INVALID_PROGRAM_ID\n  | typeof SYSTEM_ERROR__MAX_SEED_LENGTH_EXCEEDED\n  | typeof SYSTEM_ERROR__NONCE_BLOCKHASH_NOT_EXPIRED\n  | typeof SYSTEM_ERROR__NONCE_NO_RECENT_BLOCKHASHES\n  | typeof SYSTEM_ERROR__NONCE_UNEXPECTED_BLOCKHASH_VALUE\n  | typeof SYSTEM_ERROR__RESULT_WITH_NEGATIVE_LAMPORTS;\n\nlet systemErrorMessages: Record<SystemError, string> | undefined;\nif (process.env.NODE_ENV !== 'production') {\n  systemErrorMessages = {\n    [SYSTEM_ERROR__ACCOUNT_ALREADY_IN_USE]: `an account with the same address already exists`,\n    [SYSTEM_ERROR__ADDRESS_WITH_SEED_MISMATCH]: `provided address does not match addressed derived from seed`,\n    [SYSTEM_ERROR__INVALID_ACCOUNT_DATA_LENGTH]: `cannot allocate account data of this length`,\n    [SYSTEM_ERROR__INVALID_PROGRAM_ID]: `cannot assign account to this program id`,\n    [SYSTEM_ERROR__MAX_SEED_LENGTH_EXCEEDED]: `length of requested seed is too long`,\n    [SYSTEM_ERROR__NONCE_BLOCKHASH_NOT_EXPIRED]: `stored nonce is still in recent_blockhashes`,\n    [SYSTEM_ERROR__NONCE_NO_RECENT_BLOCKHASHES]: `advancing stored nonce requires a populated RecentBlockhashes sysvar`,\n    [SYSTEM_ERROR__NONCE_UNEXPECTED_BLOCKHASH_VALUE]: `specified nonce does not match stored nonce`,\n    [SYSTEM_ERROR__RESULT_WITH_NEGATIVE_LAMPORTS]: `account does not have enough SOL to perform the operation`,\n  };\n}\n\nexport function getSystemErrorMessage(code: SystemError): string {\n  if (process.env.NODE_ENV !== 'production') {\n    return (systemErrorMessages as Record<SystemError, string>)[code];\n  }\n\n  return 'Error message not available in production bundles.';\n}\n\nexport function isSystemError<TProgramErrorCode extends SystemError>(\n  error: unknown,\n  transactionMessage: {\n    instructions: Record<number, { programAddress: Address }>;\n  },\n  code?: TProgramErrorCode\n): error is SolanaError<typeof SOLANA_ERROR__INSTRUCTION_ERROR__CUSTOM> &\n  Readonly<{ context: Readonly<{ code: TProgramErrorCode }> }> {\n  return isProgramError<TProgramErrorCode>(\n    error,\n    transactionMessage,\n    SYSTEM_PROGRAM_ADDRESS,\n    code\n  );\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  AccountRole,\n  isProgramDerivedAddress,\n  isTransactionSigner as kitIsTransactionSigner,\n  type Address,\n  type IAccountMeta,\n  type IAccountSignerMeta,\n  type ProgramDerivedAddress,\n  type TransactionSigner,\n  upgradeRoleToSigner,\n} from '@solana/kit';\n\n/**\n * Asserts that the given value is not null or undefined.\n * @internal\n */\nexport function expectSome<T>(value: T | null | undefined): T {\n  if (value == null) {\n    throw new Error('Expected a value but received null or undefined.');\n  }\n  return value;\n}\n\n/**\n * Asserts that the given value is a PublicKey.\n * @internal\n */\nexport function expectAddress<T extends string = string>(\n  value:\n    | Address<T>\n    | ProgramDerivedAddress<T>\n    | TransactionSigner<T>\n    | null\n    | undefined\n): Address<T> {\n  if (!value) {\n    throw new Error('Expected a Address.');\n  }\n  if (typeof value === 'object' && 'address' in value) {\n    return value.address;\n  }\n  if (Array.isArray(value)) {\n    return value[0];\n  }\n  return value as Address<T>;\n}\n\n/**\n * Asserts that the given value is a PDA.\n * @internal\n */\nexport function expectProgramDerivedAddress<T extends string = string>(\n  value:\n    | Address<T>\n    | ProgramDerivedAddress<T>\n    | TransactionSigner<T>\n    | null\n    | undefined\n): ProgramDerivedAddress<T> {\n  if (!value || !Array.isArray(value) || !isProgramDerivedAddress(value)) {\n    throw new Error('Expected a ProgramDerivedAddress.');\n  }\n  return value;\n}\n\n/**\n * Asserts that the given value is a TransactionSigner.\n * @internal\n */\nexport function expectTransactionSigner<T extends string = string>(\n  value:\n    | Address<T>\n    | ProgramDerivedAddress<T>\n    | TransactionSigner<T>\n    | null\n    | undefined\n): TransactionSigner<T> {\n  if (!value || !isTransactionSigner(value)) {\n    throw new Error('Expected a TransactionSigner.');\n  }\n  return value;\n}\n\n/**\n * Defines an instruction account to resolve.\n * @internal\n */\nexport type ResolvedAccount<\n  T extends string = string,\n  U extends\n    | Address<T>\n    | ProgramDerivedAddress<T>\n    | TransactionSigner<T>\n    | null =\n    | Address<T>\n    | ProgramDerivedAddress<T>\n    | TransactionSigner<T>\n    | null,\n> = {\n  isWritable: boolean;\n  value: U;\n};\n\n/**\n * Defines an instruction that stores additional bytes on-chain.\n * @internal\n */\nexport type IInstructionWithByteDelta = {\n  byteDelta: number;\n};\n\n/**\n * Get account metas and signers from resolved accounts.\n * @internal\n */\nexport function getAccountMetaFactory(\n  programAddress: Address,\n  optionalAccountStrategy: 'omitted' | 'programId'\n) {\n  return (\n    account: ResolvedAccount\n  ): IAccountMeta | IAccountSignerMeta | undefined => {\n    if (!account.value) {\n      if (optionalAccountStrategy === 'omitted') return;\n      return Object.freeze({\n        address: programAddress,\n        role: AccountRole.READONLY,\n      });\n    }\n\n    const writableRole = account.isWritable\n      ? AccountRole.WRITABLE\n      : AccountRole.READONLY;\n    return Object.freeze({\n      address: expectAddress(account.value),\n      role: isTransactionSigner(account.value)\n        ? upgradeRoleToSigner(writableRole)\n        : writableRole,\n      ...(isTransactionSigner(account.value) ? { signer: account.value } : {}),\n    });\n  };\n}\n\nexport function isTransactionSigner<TAddress extends string = string>(\n  value:\n    | Address<TAddress>\n    | ProgramDerivedAddress<TAddress>\n    | TransactionSigner<TAddress>\n): value is TransactionSigner<TAddress> {\n  return (\n    !!value &&\n    typeof value === 'object' &&\n    'address' in value &&\n    kitIsTransactionSigner(value)\n  );\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  combineCodec,\n  getStructDecoder,\n  getStructEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IAccountSignerMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n  type ReadonlyAccount,\n  type ReadonlySignerAccount,\n  type TransactionSigner,\n  type WritableAccount,\n} from '@solana/kit';\nimport { SYSTEM_PROGRAM_ADDRESS } from '../programs';\nimport { getAccountMetaFactory, type ResolvedAccount } from '../shared';\n\nexport const ADVANCE_NONCE_ACCOUNT_DISCRIMINATOR = 4;\n\nexport function getAdvanceNonceAccountDiscriminatorBytes() {\n  return getU32Encoder().encode(ADVANCE_NONCE_ACCOUNT_DISCRIMINATOR);\n}\n\nexport type AdvanceNonceAccountInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountNonceAccount extends string | IAccountMeta<string> = string,\n  TAccountRecentBlockhashesSysvar extends\n    | string\n    | IAccountMeta<string> = 'SysvarRecentB1ockHashes11111111111111111111',\n  TAccountNonceAuthority extends string | IAccountMeta<string> = string,\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<\n    [\n      TAccountNonceAccount extends string\n        ? WritableAccount<TAccountNonceAccount>\n        : TAccountNonceAccount,\n      TAccountRecentBlockhashesSysvar extends string\n        ? ReadonlyAccount<TAccountRecentBlockhashesSysvar>\n        : TAccountRecentBlockhashesSysvar,\n      TAccountNonceAuthority extends string\n        ? ReadonlySignerAccount<TAccountNonceAuthority> &\n            IAccountSignerMeta<TAccountNonceAuthority>\n        : TAccountNonceAuthority,\n      ...TRemainingAccounts,\n    ]\n  >;\n\nexport type AdvanceNonceAccountInstructionData = { discriminator: number };\n\nexport type AdvanceNonceAccountInstructionDataArgs = {};\n\nexport function getAdvanceNonceAccountInstructionDataEncoder(): Encoder<AdvanceNonceAccountInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([['discriminator', getU32Encoder()]]),\n    (value) => ({\n      ...value,\n      discriminator: ADVANCE_NONCE_ACCOUNT_DISCRIMINATOR,\n    })\n  );\n}\n\nexport function getAdvanceNonceAccountInstructionDataDecoder(): Decoder<AdvanceNonceAccountInstructionData> {\n  return getStructDecoder([['discriminator', getU32Decoder()]]);\n}\n\nexport function getAdvanceNonceAccountInstructionDataCodec(): Codec<\n  AdvanceNonceAccountInstructionDataArgs,\n  AdvanceNonceAccountInstructionData\n> {\n  return combineCodec(\n    getAdvanceNonceAccountInstructionDataEncoder(),\n    getAdvanceNonceAccountInstructionDataDecoder()\n  );\n}\n\nexport type AdvanceNonceAccountInput<\n  TAccountNonceAccount extends string = string,\n  TAccountRecentBlockhashesSysvar extends string = string,\n  TAccountNonceAuthority extends string = string,\n> = {\n  nonceAccount: Address<TAccountNonceAccount>;\n  recentBlockhashesSysvar?: Address<TAccountRecentBlockhashesSysvar>;\n  nonceAuthority: TransactionSigner<TAccountNonceAuthority>;\n};\n\nexport function getAdvanceNonceAccountInstruction<\n  TAccountNonceAccount extends string,\n  TAccountRecentBlockhashesSysvar extends string,\n  TAccountNonceAuthority extends string,\n  TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS,\n>(\n  input: AdvanceNonceAccountInput<\n    TAccountNonceAccount,\n    TAccountRecentBlockhashesSysvar,\n    TAccountNonceAuthority\n  >,\n  config?: { programAddress?: TProgramAddress }\n): AdvanceNonceAccountInstruction<\n  TProgramAddress,\n  TAccountNonceAccount,\n  TAccountRecentBlockhashesSysvar,\n  TAccountNonceAuthority\n> {\n  // Program address.\n  const programAddress = config?.programAddress ?? SYSTEM_PROGRAM_ADDRESS;\n\n  // Original accounts.\n  const originalAccounts = {\n    nonceAccount: { value: input.nonceAccount ?? null, isWritable: true },\n    recentBlockhashesSysvar: {\n      value: input.recentBlockhashesSysvar ?? null,\n      isWritable: false,\n    },\n    nonceAuthority: { value: input.nonceAuthority ?? null, isWritable: false },\n  };\n  const accounts = originalAccounts as Record<\n    keyof typeof originalAccounts,\n    ResolvedAccount\n  >;\n\n  // Resolve default values.\n  if (!accounts.recentBlockhashesSysvar.value) {\n    accounts.recentBlockhashesSysvar.value =\n      'SysvarRecentB1ockHashes11111111111111111111' as Address<'SysvarRecentB1ockHashes11111111111111111111'>;\n  }\n\n  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');\n  const instruction = {\n    accounts: [\n      getAccountMeta(accounts.nonceAccount),\n      getAccountMeta(accounts.recentBlockhashesSysvar),\n      getAccountMeta(accounts.nonceAuthority),\n    ],\n    programAddress,\n    data: getAdvanceNonceAccountInstructionDataEncoder().encode({}),\n  } as AdvanceNonceAccountInstruction<\n    TProgramAddress,\n    TAccountNonceAccount,\n    TAccountRecentBlockhashesSysvar,\n    TAccountNonceAuthority\n  >;\n\n  return instruction;\n}\n\nexport type ParsedAdvanceNonceAccountInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],\n> = {\n  programAddress: Address<TProgram>;\n  accounts: {\n    nonceAccount: TAccountMetas[0];\n    recentBlockhashesSysvar: TAccountMetas[1];\n    nonceAuthority: TAccountMetas[2];\n  };\n  data: AdvanceNonceAccountInstructionData;\n};\n\nexport function parseAdvanceNonceAccountInstruction<\n  TProgram extends string,\n  TAccountMetas extends readonly IAccountMeta[],\n>(\n  instruction: IInstruction<TProgram> &\n    IInstructionWithAccounts<TAccountMetas> &\n    IInstructionWithData<Uint8Array>\n): ParsedAdvanceNonceAccountInstruction<TProgram, TAccountMetas> {\n  if (instruction.accounts.length < 3) {\n    // TODO: Coded error.\n    throw new Error('Not enough accounts');\n  }\n  let accountIndex = 0;\n  const getNextAccount = () => {\n    const accountMeta = instruction.accounts![accountIndex]!;\n    accountIndex += 1;\n    return accountMeta;\n  };\n  return {\n    programAddress: instruction.programAddress,\n    accounts: {\n      nonceAccount: getNextAccount(),\n      recentBlockhashesSysvar: getNextAccount(),\n      nonceAuthority: getNextAccount(),\n    },\n    data: getAdvanceNonceAccountInstructionDataDecoder().decode(\n      instruction.data\n    ),\n  };\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  combineCodec,\n  getStructDecoder,\n  getStructEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  getU64Decoder,\n  getU64Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IAccountSignerMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n  type TransactionSigner,\n  type WritableSignerAccount,\n} from '@solana/kit';\nimport { SYSTEM_PROGRAM_ADDRESS } from '../programs';\nimport { getAccountMetaFactory, type ResolvedAccount } from '../shared';\n\nexport const ALLOCATE_DISCRIMINATOR = 8;\n\nexport function getAllocateDiscriminatorBytes() {\n  return getU32Encoder().encode(ALLOCATE_DISCRIMINATOR);\n}\n\nexport type AllocateInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountNewAccount extends string | IAccountMeta<string> = string,\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<\n    [\n      TAccountNewAccount extends string\n        ? WritableSignerAccount<TAccountNewAccount> &\n            IAccountSignerMeta<TAccountNewAccount>\n        : TAccountNewAccount,\n      ...TRemainingAccounts,\n    ]\n  >;\n\nexport type AllocateInstructionData = { discriminator: number; space: bigint };\n\nexport type AllocateInstructionDataArgs = { space: number | bigint };\n\nexport function getAllocateInstructionDataEncoder(): Encoder<AllocateInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([\n      ['discriminator', getU32Encoder()],\n      ['space', getU64Encoder()],\n    ]),\n    (value) => ({ ...value, discriminator: ALLOCATE_DISCRIMINATOR })\n  );\n}\n\nexport function getAllocateInstructionDataDecoder(): Decoder<AllocateInstructionData> {\n  return getStructDecoder([\n    ['discriminator', getU32Decoder()],\n    ['space', getU64Decoder()],\n  ]);\n}\n\nexport function getAllocateInstructionDataCodec(): Codec<\n  AllocateInstructionDataArgs,\n  AllocateInstructionData\n> {\n  return combineCodec(\n    getAllocateInstructionDataEncoder(),\n    getAllocateInstructionDataDecoder()\n  );\n}\n\nexport type AllocateInput<TAccountNewAccount extends string = string> = {\n  newAccount: TransactionSigner<TAccountNewAccount>;\n  space: AllocateInstructionDataArgs['space'];\n};\n\nexport function getAllocateInstruction<\n  TAccountNewAccount extends string,\n  TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS,\n>(\n  input: AllocateInput<TAccountNewAccount>,\n  config?: { programAddress?: TProgramAddress }\n): AllocateInstruction<TProgramAddress, TAccountNewAccount> {\n  // Program address.\n  const programAddress = config?.programAddress ?? SYSTEM_PROGRAM_ADDRESS;\n\n  // Original accounts.\n  const originalAccounts = {\n    newAccount: { value: input.newAccount ?? null, isWritable: true },\n  };\n  const accounts = originalAccounts as Record<\n    keyof typeof originalAccounts,\n    ResolvedAccount\n  >;\n\n  // Original args.\n  const args = { ...input };\n\n  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');\n  const instruction = {\n    accounts: [getAccountMeta(accounts.newAccount)],\n    programAddress,\n    data: getAllocateInstructionDataEncoder().encode(\n      args as AllocateInstructionDataArgs\n    ),\n  } as AllocateInstruction<TProgramAddress, TAccountNewAccount>;\n\n  return instruction;\n}\n\nexport type ParsedAllocateInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],\n> = {\n  programAddress: Address<TProgram>;\n  accounts: {\n    newAccount: TAccountMetas[0];\n  };\n  data: AllocateInstructionData;\n};\n\nexport function parseAllocateInstruction<\n  TProgram extends string,\n  TAccountMetas extends readonly IAccountMeta[],\n>(\n  instruction: IInstruction<TProgram> &\n    IInstructionWithAccounts<TAccountMetas> &\n    IInstructionWithData<Uint8Array>\n): ParsedAllocateInstruction<TProgram, TAccountMetas> {\n  if (instruction.accounts.length < 1) {\n    // TODO: Coded error.\n    throw new Error('Not enough accounts');\n  }\n  let accountIndex = 0;\n  const getNextAccount = () => {\n    const accountMeta = instruction.accounts![accountIndex]!;\n    accountIndex += 1;\n    return accountMeta;\n  };\n  return {\n    programAddress: instruction.programAddress,\n    accounts: {\n      newAccount: getNextAccount(),\n    },\n    data: getAllocateInstructionDataDecoder().decode(instruction.data),\n  };\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  addDecoderSizePrefix,\n  addEncoderSizePrefix,\n  combineCodec,\n  getAddressDecoder,\n  getAddressEncoder,\n  getStructDecoder,\n  getStructEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  getU64Decoder,\n  getU64Encoder,\n  getUtf8Decoder,\n  getUtf8Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IAccountSignerMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n  type ReadonlySignerAccount,\n  type TransactionSigner,\n  type WritableAccount,\n} from '@solana/kit';\nimport { SYSTEM_PROGRAM_ADDRESS } from '../programs';\nimport { getAccountMetaFactory, type ResolvedAccount } from '../shared';\n\nexport const ALLOCATE_WITH_SEED_DISCRIMINATOR = 9;\n\nexport function getAllocateWithSeedDiscriminatorBytes() {\n  return getU32Encoder().encode(ALLOCATE_WITH_SEED_DISCRIMINATOR);\n}\n\nexport type AllocateWithSeedInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountNewAccount extends string | IAccountMeta<string> = string,\n  TAccountBaseAccount extends string | IAccountMeta<string> = string,\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<\n    [\n      TAccountNewAccount extends string\n        ? WritableAccount<TAccountNewAccount>\n        : TAccountNewAccount,\n      TAccountBaseAccount extends string\n        ? ReadonlySignerAccount<TAccountBaseAccount> &\n            IAccountSignerMeta<TAccountBaseAccount>\n        : TAccountBaseAccount,\n      ...TRemainingAccounts,\n    ]\n  >;\n\nexport type AllocateWithSeedInstructionData = {\n  discriminator: number;\n  base: Address;\n  seed: string;\n  space: bigint;\n  programAddress: Address;\n};\n\nexport type AllocateWithSeedInstructionDataArgs = {\n  base: Address;\n  seed: string;\n  space: number | bigint;\n  programAddress: Address;\n};\n\nexport function getAllocateWithSeedInstructionDataEncoder(): Encoder<AllocateWithSeedInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([\n      ['discriminator', getU32Encoder()],\n      ['base', getAddressEncoder()],\n      ['seed', addEncoderSizePrefix(getUtf8Encoder(), getU64Encoder())],\n      ['space', getU64Encoder()],\n      ['programAddress', getAddressEncoder()],\n    ]),\n    (value) => ({ ...value, discriminator: ALLOCATE_WITH_SEED_DISCRIMINATOR })\n  );\n}\n\nexport function getAllocateWithSeedInstructionDataDecoder(): Decoder<AllocateWithSeedInstructionData> {\n  return getStructDecoder([\n    ['discriminator', getU32Decoder()],\n    ['base', getAddressDecoder()],\n    ['seed', addDecoderSizePrefix(getUtf8Decoder(), getU64Decoder())],\n    ['space', getU64Decoder()],\n    ['programAddress', getAddressDecoder()],\n  ]);\n}\n\nexport function getAllocateWithSeedInstructionDataCodec(): Codec<\n  AllocateWithSeedInstructionDataArgs,\n  AllocateWithSeedInstructionData\n> {\n  return combineCodec(\n    getAllocateWithSeedInstructionDataEncoder(),\n    getAllocateWithSeedInstructionDataDecoder()\n  );\n}\n\nexport type AllocateWithSeedInput<\n  TAccountNewAccount extends string = string,\n  TAccountBaseAccount extends string = string,\n> = {\n  newAccount: Address<TAccountNewAccount>;\n  baseAccount: TransactionSigner<TAccountBaseAccount>;\n  base: AllocateWithSeedInstructionDataArgs['base'];\n  seed: AllocateWithSeedInstructionDataArgs['seed'];\n  space: AllocateWithSeedInstructionDataArgs['space'];\n  programAddress: AllocateWithSeedInstructionDataArgs['programAddress'];\n};\n\nexport function getAllocateWithSeedInstruction<\n  TAccountNewAccount extends string,\n  TAccountBaseAccount extends string,\n  TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS,\n>(\n  input: AllocateWithSeedInput<TAccountNewAccount, TAccountBaseAccount>,\n  config?: { programAddress?: TProgramAddress }\n): AllocateWithSeedInstruction<\n  TProgramAddress,\n  TAccountNewAccount,\n  TAccountBaseAccount\n> {\n  // Program address.\n  const programAddress = config?.programAddress ?? SYSTEM_PROGRAM_ADDRESS;\n\n  // Original accounts.\n  const originalAccounts = {\n    newAccount: { value: input.newAccount ?? null, isWritable: true },\n    baseAccount: { value: input.baseAccount ?? null, isWritable: false },\n  };\n  const accounts = originalAccounts as Record<\n    keyof typeof originalAccounts,\n    ResolvedAccount\n  >;\n\n  // Original args.\n  const args = { ...input };\n\n  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');\n  const instruction = {\n    accounts: [\n      getAccountMeta(accounts.newAccount),\n      getAccountMeta(accounts.baseAccount),\n    ],\n    programAddress,\n    data: getAllocateWithSeedInstructionDataEncoder().encode(\n      args as AllocateWithSeedInstructionDataArgs\n    ),\n  } as AllocateWithSeedInstruction<\n    TProgramAddress,\n    TAccountNewAccount,\n    TAccountBaseAccount\n  >;\n\n  return instruction;\n}\n\nexport type ParsedAllocateWithSeedInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],\n> = {\n  programAddress: Address<TProgram>;\n  accounts: {\n    newAccount: TAccountMetas[0];\n    baseAccount: TAccountMetas[1];\n  };\n  data: AllocateWithSeedInstructionData;\n};\n\nexport function parseAllocateWithSeedInstruction<\n  TProgram extends string,\n  TAccountMetas extends readonly IAccountMeta[],\n>(\n  instruction: IInstruction<TProgram> &\n    IInstructionWithAccounts<TAccountMetas> &\n    IInstructionWithData<Uint8Array>\n): ParsedAllocateWithSeedInstruction<TProgram, TAccountMetas> {\n  if (instruction.accounts.length < 2) {\n    // TODO: Coded error.\n    throw new Error('Not enough accounts');\n  }\n  let accountIndex = 0;\n  const getNextAccount = () => {\n    const accountMeta = instruction.accounts![accountIndex]!;\n    accountIndex += 1;\n    return accountMeta;\n  };\n  return {\n    programAddress: instruction.programAddress,\n    accounts: {\n      newAccount: getNextAccount(),\n      baseAccount: getNextAccount(),\n    },\n    data: getAllocateWithSeedInstructionDataDecoder().decode(instruction.data),\n  };\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  combineCodec,\n  getAddressDecoder,\n  getAddressEncoder,\n  getStructDecoder,\n  getStructEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IAccountSignerMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n  type TransactionSigner,\n  type WritableSignerAccount,\n} from '@solana/kit';\nimport { SYSTEM_PROGRAM_ADDRESS } from '../programs';\nimport { getAccountMetaFactory, type ResolvedAccount } from '../shared';\n\nexport const ASSIGN_DISCRIMINATOR = 1;\n\nexport function getAssignDiscriminatorBytes() {\n  return getU32Encoder().encode(ASSIGN_DISCRIMINATOR);\n}\n\nexport type AssignInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountAccount extends string | IAccountMeta<string> = string,\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<\n    [\n      TAccountAccount extends string\n        ? WritableSignerAccount<TAccountAccount> &\n            IAccountSignerMeta<TAccountAccount>\n        : TAccountAccount,\n      ...TRemainingAccounts,\n    ]\n  >;\n\nexport type AssignInstructionData = {\n  discriminator: number;\n  programAddress: Address;\n};\n\nexport type AssignInstructionDataArgs = { programAddress: Address };\n\nexport function getAssignInstructionDataEncoder(): Encoder<AssignInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([\n      ['discriminator', getU32Encoder()],\n      ['programAddress', getAddressEncoder()],\n    ]),\n    (value) => ({ ...value, discriminator: ASSIGN_DISCRIMINATOR })\n  );\n}\n\nexport function getAssignInstructionDataDecoder(): Decoder<AssignInstructionData> {\n  return getStructDecoder([\n    ['discriminator', getU32Decoder()],\n    ['programAddress', getAddressDecoder()],\n  ]);\n}\n\nexport function getAssignInstructionDataCodec(): Codec<\n  AssignInstructionDataArgs,\n  AssignInstructionData\n> {\n  return combineCodec(\n    getAssignInstructionDataEncoder(),\n    getAssignInstructionDataDecoder()\n  );\n}\n\nexport type AssignInput<TAccountAccount extends string = string> = {\n  account: TransactionSigner<TAccountAccount>;\n  programAddress: AssignInstructionDataArgs['programAddress'];\n};\n\nexport function getAssignInstruction<\n  TAccountAccount extends string,\n  TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS,\n>(\n  input: AssignInput<TAccountAccount>,\n  config?: { programAddress?: TProgramAddress }\n): AssignInstruction<TProgramAddress, TAccountAccount> {\n  // Program address.\n  const programAddress = config?.programAddress ?? SYSTEM_PROGRAM_ADDRESS;\n\n  // Original accounts.\n  const originalAccounts = {\n    account: { value: input.account ?? null, isWritable: true },\n  };\n  const accounts = originalAccounts as Record<\n    keyof typeof originalAccounts,\n    ResolvedAccount\n  >;\n\n  // Original args.\n  const args = { ...input };\n\n  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');\n  const instruction = {\n    accounts: [getAccountMeta(accounts.account)],\n    programAddress,\n    data: getAssignInstructionDataEncoder().encode(\n      args as AssignInstructionDataArgs\n    ),\n  } as AssignInstruction<TProgramAddress, TAccountAccount>;\n\n  return instruction;\n}\n\nexport type ParsedAssignInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],\n> = {\n  programAddress: Address<TProgram>;\n  accounts: {\n    account: TAccountMetas[0];\n  };\n  data: AssignInstructionData;\n};\n\nexport function parseAssignInstruction<\n  TProgram extends string,\n  TAccountMetas extends readonly IAccountMeta[],\n>(\n  instruction: IInstruction<TProgram> &\n    IInstructionWithAccounts<TAccountMetas> &\n    IInstructionWithData<Uint8Array>\n): ParsedAssignInstruction<TProgram, TAccountMetas> {\n  if (instruction.accounts.length < 1) {\n    // TODO: Coded error.\n    throw new Error('Not enough accounts');\n  }\n  let accountIndex = 0;\n  const getNextAccount = () => {\n    const accountMeta = instruction.accounts![accountIndex]!;\n    accountIndex += 1;\n    return accountMeta;\n  };\n  return {\n    programAddress: instruction.programAddress,\n    accounts: {\n      account: getNextAccount(),\n    },\n    data: getAssignInstructionDataDecoder().decode(instruction.data),\n  };\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  addDecoderSizePrefix,\n  addEncoderSizePrefix,\n  combineCodec,\n  getAddressDecoder,\n  getAddressEncoder,\n  getStructDecoder,\n  getStructEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  getU64Decoder,\n  getU64Encoder,\n  getUtf8Decoder,\n  getUtf8Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IAccountSignerMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n  type ReadonlySignerAccount,\n  type TransactionSigner,\n  type WritableAccount,\n} from '@solana/kit';\nimport { SYSTEM_PROGRAM_ADDRESS } from '../programs';\nimport { getAccountMetaFactory, type ResolvedAccount } from '../shared';\n\nexport const ASSIGN_WITH_SEED_DISCRIMINATOR = 10;\n\nexport function getAssignWithSeedDiscriminatorBytes() {\n  return getU32Encoder().encode(ASSIGN_WITH_SEED_DISCRIMINATOR);\n}\n\nexport type AssignWithSeedInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountAccount extends string | IAccountMeta<string> = string,\n  TAccountBaseAccount extends string | IAccountMeta<string> = string,\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<\n    [\n      TAccountAccount extends string\n        ? WritableAccount<TAccountAccount>\n        : TAccountAccount,\n      TAccountBaseAccount extends string\n        ? ReadonlySignerAccount<TAccountBaseAccount> &\n            IAccountSignerMeta<TAccountBaseAccount>\n        : TAccountBaseAccount,\n      ...TRemainingAccounts,\n    ]\n  >;\n\nexport type AssignWithSeedInstructionData = {\n  discriminator: number;\n  base: Address;\n  seed: string;\n  programAddress: Address;\n};\n\nexport type AssignWithSeedInstructionDataArgs = {\n  base: Address;\n  seed: string;\n  programAddress: Address;\n};\n\nexport function getAssignWithSeedInstructionDataEncoder(): Encoder<AssignWithSeedInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([\n      ['discriminator', getU32Encoder()],\n      ['base', getAddressEncoder()],\n      ['seed', addEncoderSizePrefix(getUtf8Encoder(), getU64Encoder())],\n      ['programAddress', getAddressEncoder()],\n    ]),\n    (value) => ({ ...value, discriminator: ASSIGN_WITH_SEED_DISCRIMINATOR })\n  );\n}\n\nexport function getAssignWithSeedInstructionDataDecoder(): Decoder<AssignWithSeedInstructionData> {\n  return getStructDecoder([\n    ['discriminator', getU32Decoder()],\n    ['base', getAddressDecoder()],\n    ['seed', addDecoderSizePrefix(getUtf8Decoder(), getU64Decoder())],\n    ['programAddress', getAddressDecoder()],\n  ]);\n}\n\nexport function getAssignWithSeedInstructionDataCodec(): Codec<\n  AssignWithSeedInstructionDataArgs,\n  AssignWithSeedInstructionData\n> {\n  return combineCodec(\n    getAssignWithSeedInstructionDataEncoder(),\n    getAssignWithSeedInstructionDataDecoder()\n  );\n}\n\nexport type AssignWithSeedInput<\n  TAccountAccount extends string = string,\n  TAccountBaseAccount extends string = string,\n> = {\n  account: Address<TAccountAccount>;\n  baseAccount: TransactionSigner<TAccountBaseAccount>;\n  base: AssignWithSeedInstructionDataArgs['base'];\n  seed: AssignWithSeedInstructionDataArgs['seed'];\n  programAddress: AssignWithSeedInstructionDataArgs['programAddress'];\n};\n\nexport function getAssignWithSeedInstruction<\n  TAccountAccount extends string,\n  TAccountBaseAccount extends string,\n  TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS,\n>(\n  input: AssignWithSeedInput<TAccountAccount, TAccountBaseAccount>,\n  config?: { programAddress?: TProgramAddress }\n): AssignWithSeedInstruction<\n  TProgramAddress,\n  TAccountAccount,\n  TAccountBaseAccount\n> {\n  // Program address.\n  const programAddress = config?.programAddress ?? SYSTEM_PROGRAM_ADDRESS;\n\n  // Original accounts.\n  const originalAccounts = {\n    account: { value: input.account ?? null, isWritable: true },\n    baseAccount: { value: input.baseAccount ?? null, isWritable: false },\n  };\n  const accounts = originalAccounts as Record<\n    keyof typeof originalAccounts,\n    ResolvedAccount\n  >;\n\n  // Original args.\n  const args = { ...input };\n\n  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');\n  const instruction = {\n    accounts: [\n      getAccountMeta(accounts.account),\n      getAccountMeta(accounts.baseAccount),\n    ],\n    programAddress,\n    data: getAssignWithSeedInstructionDataEncoder().encode(\n      args as AssignWithSeedInstructionDataArgs\n    ),\n  } as AssignWithSeedInstruction<\n    TProgramAddress,\n    TAccountAccount,\n    TAccountBaseAccount\n  >;\n\n  return instruction;\n}\n\nexport type ParsedAssignWithSeedInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],\n> = {\n  programAddress: Address<TProgram>;\n  accounts: {\n    account: TAccountMetas[0];\n    baseAccount: TAccountMetas[1];\n  };\n  data: AssignWithSeedInstructionData;\n};\n\nexport function parseAssignWithSeedInstruction<\n  TProgram extends string,\n  TAccountMetas extends readonly IAccountMeta[],\n>(\n  instruction: IInstruction<TProgram> &\n    IInstructionWithAccounts<TAccountMetas> &\n    IInstructionWithData<Uint8Array>\n): ParsedAssignWithSeedInstruction<TProgram, TAccountMetas> {\n  if (instruction.accounts.length < 2) {\n    // TODO: Coded error.\n    throw new Error('Not enough accounts');\n  }\n  let accountIndex = 0;\n  const getNextAccount = () => {\n    const accountMeta = instruction.accounts![accountIndex]!;\n    accountIndex += 1;\n    return accountMeta;\n  };\n  return {\n    programAddress: instruction.programAddress,\n    accounts: {\n      account: getNextAccount(),\n      baseAccount: getNextAccount(),\n    },\n    data: getAssignWithSeedInstructionDataDecoder().decode(instruction.data),\n  };\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  combineCodec,\n  getAddressDecoder,\n  getAddressEncoder,\n  getStructDecoder,\n  getStructEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IAccountSignerMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n  type ReadonlySignerAccount,\n  type TransactionSigner,\n  type WritableAccount,\n} from '@solana/kit';\nimport { SYSTEM_PROGRAM_ADDRESS } from '../programs';\nimport { getAccountMetaFactory, type ResolvedAccount } from '../shared';\n\nexport const AUTHORIZE_NONCE_ACCOUNT_DISCRIMINATOR = 7;\n\nexport function getAuthorizeNonceAccountDiscriminatorBytes() {\n  return getU32Encoder().encode(AUTHORIZE_NONCE_ACCOUNT_DISCRIMINATOR);\n}\n\nexport type AuthorizeNonceAccountInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountNonceAccount extends string | IAccountMeta<string> = string,\n  TAccountNonceAuthority extends string | IAccountMeta<string> = string,\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<\n    [\n      TAccountNonceAccount extends string\n        ? WritableAccount<TAccountNonceAccount>\n        : TAccountNonceAccount,\n      TAccountNonceAuthority extends string\n        ? ReadonlySignerAccount<TAccountNonceAuthority> &\n            IAccountSignerMeta<TAccountNonceAuthority>\n        : TAccountNonceAuthority,\n      ...TRemainingAccounts,\n    ]\n  >;\n\nexport type AuthorizeNonceAccountInstructionData = {\n  discriminator: number;\n  newNonceAuthority: Address;\n};\n\nexport type AuthorizeNonceAccountInstructionDataArgs = {\n  newNonceAuthority: Address;\n};\n\nexport function getAuthorizeNonceAccountInstructionDataEncoder(): Encoder<AuthorizeNonceAccountInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([\n      ['discriminator', getU32Encoder()],\n      ['newNonceAuthority', getAddressEncoder()],\n    ]),\n    (value) => ({\n      ...value,\n      discriminator: AUTHORIZE_NONCE_ACCOUNT_DISCRIMINATOR,\n    })\n  );\n}\n\nexport function getAuthorizeNonceAccountInstructionDataDecoder(): Decoder<AuthorizeNonceAccountInstructionData> {\n  return getStructDecoder([\n    ['discriminator', getU32Decoder()],\n    ['newNonceAuthority', getAddressDecoder()],\n  ]);\n}\n\nexport function getAuthorizeNonceAccountInstructionDataCodec(): Codec<\n  AuthorizeNonceAccountInstructionDataArgs,\n  AuthorizeNonceAccountInstructionData\n> {\n  return combineCodec(\n    getAuthorizeNonceAccountInstructionDataEncoder(),\n    getAuthorizeNonceAccountInstructionDataDecoder()\n  );\n}\n\nexport type AuthorizeNonceAccountInput<\n  TAccountNonceAccount extends string = string,\n  TAccountNonceAuthority extends string = string,\n> = {\n  nonceAccount: Address<TAccountNonceAccount>;\n  nonceAuthority: TransactionSigner<TAccountNonceAuthority>;\n  newNonceAuthority: AuthorizeNonceAccountInstructionDataArgs['newNonceAuthority'];\n};\n\nexport function getAuthorizeNonceAccountInstruction<\n  TAccountNonceAccount extends string,\n  TAccountNonceAuthority extends string,\n  TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS,\n>(\n  input: AuthorizeNonceAccountInput<\n    TAccountNonceAccount,\n    TAccountNonceAuthority\n  >,\n  config?: { programAddress?: TProgramAddress }\n): AuthorizeNonceAccountInstruction<\n  TProgramAddress,\n  TAccountNonceAccount,\n  TAccountNonceAuthority\n> {\n  // Program address.\n  const programAddress = config?.programAddress ?? SYSTEM_PROGRAM_ADDRESS;\n\n  // Original accounts.\n  const originalAccounts = {\n    nonceAccount: { value: input.nonceAccount ?? null, isWritable: true },\n    nonceAuthority: { value: input.nonceAuthority ?? null, isWritable: false },\n  };\n  const accounts = originalAccounts as Record<\n    keyof typeof originalAccounts,\n    ResolvedAccount\n  >;\n\n  // Original args.\n  const args = { ...input };\n\n  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');\n  const instruction = {\n    accounts: [\n      getAccountMeta(accounts.nonceAccount),\n      getAccountMeta(accounts.nonceAuthority),\n    ],\n    programAddress,\n    data: getAuthorizeNonceAccountInstructionDataEncoder().encode(\n      args as AuthorizeNonceAccountInstructionDataArgs\n    ),\n  } as AuthorizeNonceAccountInstruction<\n    TProgramAddress,\n    TAccountNonceAccount,\n    TAccountNonceAuthority\n  >;\n\n  return instruction;\n}\n\nexport type ParsedAuthorizeNonceAccountInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],\n> = {\n  programAddress: Address<TProgram>;\n  accounts: {\n    nonceAccount: TAccountMetas[0];\n    nonceAuthority: TAccountMetas[1];\n  };\n  data: AuthorizeNonceAccountInstructionData;\n};\n\nexport function parseAuthorizeNonceAccountInstruction<\n  TProgram extends string,\n  TAccountMetas extends readonly IAccountMeta[],\n>(\n  instruction: IInstruction<TProgram> &\n    IInstructionWithAccounts<TAccountMetas> &\n    IInstructionWithData<Uint8Array>\n): ParsedAuthorizeNonceAccountInstruction<TProgram, TAccountMetas> {\n  if (instruction.accounts.length < 2) {\n    // TODO: Coded error.\n    throw new Error('Not enough accounts');\n  }\n  let accountIndex = 0;\n  const getNextAccount = () => {\n    const accountMeta = instruction.accounts![accountIndex]!;\n    accountIndex += 1;\n    return accountMeta;\n  };\n  return {\n    programAddress: instruction.programAddress,\n    accounts: {\n      nonceAccount: getNextAccount(),\n      nonceAuthority: getNextAccount(),\n    },\n    data: getAuthorizeNonceAccountInstructionDataDecoder().decode(\n      instruction.data\n    ),\n  };\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  BASE_ACCOUNT_SIZE,\n  combineCodec,\n  getAddressDecoder,\n  getAddressEncoder,\n  getStructDecoder,\n  getStructEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  getU64Decoder,\n  getU64Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IAccountSignerMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n  type TransactionSigner,\n  type WritableSignerAccount,\n} from '@solana/kit';\nimport { SYSTEM_PROGRAM_ADDRESS } from '../programs';\nimport {\n  getAccountMetaFactory,\n  type IInstructionWithByteDelta,\n  type ResolvedAccount,\n} from '../shared';\n\nexport const CREATE_ACCOUNT_DISCRIMINATOR = 0;\n\nexport function getCreateAccountDiscriminatorBytes() {\n  return getU32Encoder().encode(CREATE_ACCOUNT_DISCRIMINATOR);\n}\n\nexport type CreateAccountInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountPayer extends string | IAccountMeta<string> = string,\n  TAccountNewAccount extends string | IAccountMeta<string> = string,\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<\n    [\n      TAccountPayer extends string\n        ? WritableSignerAccount<TAccountPayer> &\n            IAccountSignerMeta<TAccountPayer>\n        : TAccountPayer,\n      TAccountNewAccount extends string\n        ? WritableSignerAccount<TAccountNewAccount> &\n            IAccountSignerMeta<TAccountNewAccount>\n        : TAccountNewAccount,\n      ...TRemainingAccounts,\n    ]\n  >;\n\nexport type CreateAccountInstructionData = {\n  discriminator: number;\n  lamports: bigint;\n  space: bigint;\n  programAddress: Address;\n};\n\nexport type CreateAccountInstructionDataArgs = {\n  lamports: number | bigint;\n  space: number | bigint;\n  programAddress: Address;\n};\n\nexport function getCreateAccountInstructionDataEncoder(): Encoder<CreateAccountInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([\n      ['discriminator', getU32Encoder()],\n      ['lamports', getU64Encoder()],\n      ['space', getU64Encoder()],\n      ['programAddress', getAddressEncoder()],\n    ]),\n    (value) => ({ ...value, discriminator: CREATE_ACCOUNT_DISCRIMINATOR })\n  );\n}\n\nexport function getCreateAccountInstructionDataDecoder(): Decoder<CreateAccountInstructionData> {\n  return getStructDecoder([\n    ['discriminator', getU32Decoder()],\n    ['lamports', getU64Decoder()],\n    ['space', getU64Decoder()],\n    ['programAddress', getAddressDecoder()],\n  ]);\n}\n\nexport function getCreateAccountInstructionDataCodec(): Codec<\n  CreateAccountInstructionDataArgs,\n  CreateAccountInstructionData\n> {\n  return combineCodec(\n    getCreateAccountInstructionDataEncoder(),\n    getCreateAccountInstructionDataDecoder()\n  );\n}\n\nexport type CreateAccountInput<\n  TAccountPayer extends string = string,\n  TAccountNewAccount extends string = string,\n> = {\n  payer: TransactionSigner<TAccountPayer>;\n  newAccount: TransactionSigner<TAccountNewAccount>;\n  lamports: CreateAccountInstructionDataArgs['lamports'];\n  space: CreateAccountInstructionDataArgs['space'];\n  programAddress: CreateAccountInstructionDataArgs['programAddress'];\n};\n\nexport function getCreateAccountInstruction<\n  TAccountPayer extends string,\n  TAccountNewAccount extends string,\n  TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS,\n>(\n  input: CreateAccountInput<TAccountPayer, TAccountNewAccount>,\n  config?: { programAddress?: TProgramAddress }\n): CreateAccountInstruction<\n  TProgramAddress,\n  TAccountPayer,\n  TAccountNewAccount\n> &\n  IInstructionWithByteDelta {\n  // Program address.\n  const programAddress = config?.programAddress ?? SYSTEM_PROGRAM_ADDRESS;\n\n  // Original accounts.\n  const originalAccounts = {\n    payer: { value: input.payer ?? null, isWritable: true },\n    newAccount: { value: input.newAccount ?? null, isWritable: true },\n  };\n  const accounts = originalAccounts as Record<\n    keyof typeof originalAccounts,\n    ResolvedAccount\n  >;\n\n  // Original args.\n  const args = { ...input };\n\n  // Bytes created or reallocated by the instruction.\n  const byteDelta: number = [Number(args.space) + BASE_ACCOUNT_SIZE].reduce(\n    (a, b) => a + b,\n    0\n  );\n\n  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');\n  const instruction = {\n    accounts: [\n      getAccountMeta(accounts.payer),\n      getAccountMeta(accounts.newAccount),\n    ],\n    programAddress,\n    data: getCreateAccountInstructionDataEncoder().encode(\n      args as CreateAccountInstructionDataArgs\n    ),\n  } as CreateAccountInstruction<\n    TProgramAddress,\n    TAccountPayer,\n    TAccountNewAccount\n  >;\n\n  return Object.freeze({ ...instruction, byteDelta });\n}\n\nexport type ParsedCreateAccountInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],\n> = {\n  programAddress: Address<TProgram>;\n  accounts: {\n    payer: TAccountMetas[0];\n    newAccount: TAccountMetas[1];\n  };\n  data: CreateAccountInstructionData;\n};\n\nexport function parseCreateAccountInstruction<\n  TProgram extends string,\n  TAccountMetas extends readonly IAccountMeta[],\n>(\n  instruction: IInstruction<TProgram> &\n    IInstructionWithAccounts<TAccountMetas> &\n    IInstructionWithData<Uint8Array>\n): ParsedCreateAccountInstruction<TProgram, TAccountMetas> {\n  if (instruction.accounts.length < 2) {\n    // TODO: Coded error.\n    throw new Error('Not enough accounts');\n  }\n  let accountIndex = 0;\n  const getNextAccount = () => {\n    const accountMeta = instruction.accounts![accountIndex]!;\n    accountIndex += 1;\n    return accountMeta;\n  };\n  return {\n    programAddress: instruction.programAddress,\n    accounts: {\n      payer: getNextAccount(),\n      newAccount: getNextAccount(),\n    },\n    data: getCreateAccountInstructionDataDecoder().decode(instruction.data),\n  };\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  addDecoderSizePrefix,\n  addEncoderSizePrefix,\n  combineCodec,\n  getAddressDecoder,\n  getAddressEncoder,\n  getStructDecoder,\n  getStructEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  getU64Decoder,\n  getU64Encoder,\n  getUtf8Decoder,\n  getUtf8Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IAccountSignerMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n  type ReadonlySignerAccount,\n  type TransactionSigner,\n  type WritableAccount,\n  type WritableSignerAccount,\n} from '@solana/kit';\nimport { SYSTEM_PROGRAM_ADDRESS } from '../programs';\nimport { getAccountMetaFactory, type ResolvedAccount } from '../shared';\n\nexport const CREATE_ACCOUNT_WITH_SEED_DISCRIMINATOR = 3;\n\nexport function getCreateAccountWithSeedDiscriminatorBytes() {\n  return getU32Encoder().encode(CREATE_ACCOUNT_WITH_SEED_DISCRIMINATOR);\n}\n\nexport type CreateAccountWithSeedInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountPayer extends string | IAccountMeta<string> = string,\n  TAccountNewAccount extends string | IAccountMeta<string> = string,\n  TAccountBaseAccount extends string | IAccountMeta<string> = string,\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<\n    [\n      TAccountPayer extends string\n        ? WritableSignerAccount<TAccountPayer> &\n            IAccountSignerMeta<TAccountPayer>\n        : TAccountPayer,\n      TAccountNewAccount extends string\n        ? WritableAccount<TAccountNewAccount>\n        : TAccountNewAccount,\n      TAccountBaseAccount extends string\n        ? ReadonlySignerAccount<TAccountBaseAccount> &\n            IAccountSignerMeta<TAccountBaseAccount>\n        : TAccountBaseAccount,\n      ...TRemainingAccounts,\n    ]\n  >;\n\nexport type CreateAccountWithSeedInstructionData = {\n  discriminator: number;\n  base: Address;\n  seed: string;\n  amount: bigint;\n  space: bigint;\n  programAddress: Address;\n};\n\nexport type CreateAccountWithSeedInstructionDataArgs = {\n  base: Address;\n  seed: string;\n  amount: number | bigint;\n  space: number | bigint;\n  programAddress: Address;\n};\n\nexport function getCreateAccountWithSeedInstructionDataEncoder(): Encoder<CreateAccountWithSeedInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([\n      ['discriminator', getU32Encoder()],\n      ['base', getAddressEncoder()],\n      ['seed', addEncoderSizePrefix(getUtf8Encoder(), getU64Encoder())],\n      ['amount', getU64Encoder()],\n      ['space', getU64Encoder()],\n      ['programAddress', getAddressEncoder()],\n    ]),\n    (value) => ({\n      ...value,\n      discriminator: CREATE_ACCOUNT_WITH_SEED_DISCRIMINATOR,\n    })\n  );\n}\n\nexport function getCreateAccountWithSeedInstructionDataDecoder(): Decoder<CreateAccountWithSeedInstructionData> {\n  return getStructDecoder([\n    ['discriminator', getU32Decoder()],\n    ['base', getAddressDecoder()],\n    ['seed', addDecoderSizePrefix(getUtf8Decoder(), getU64Decoder())],\n    ['amount', getU64Decoder()],\n    ['space', getU64Decoder()],\n    ['programAddress', getAddressDecoder()],\n  ]);\n}\n\nexport function getCreateAccountWithSeedInstructionDataCodec(): Codec<\n  CreateAccountWithSeedInstructionDataArgs,\n  CreateAccountWithSeedInstructionData\n> {\n  return combineCodec(\n    getCreateAccountWithSeedInstructionDataEncoder(),\n    getCreateAccountWithSeedInstructionDataDecoder()\n  );\n}\n\nexport type CreateAccountWithSeedInput<\n  TAccountPayer extends string = string,\n  TAccountNewAccount extends string = string,\n  TAccountBaseAccount extends string = string,\n> = {\n  payer: TransactionSigner<TAccountPayer>;\n  newAccount: Address<TAccountNewAccount>;\n  baseAccount: TransactionSigner<TAccountBaseAccount>;\n  base: CreateAccountWithSeedInstructionDataArgs['base'];\n  seed: CreateAccountWithSeedInstructionDataArgs['seed'];\n  amount: CreateAccountWithSeedInstructionDataArgs['amount'];\n  space: CreateAccountWithSeedInstructionDataArgs['space'];\n  programAddress: CreateAccountWithSeedInstructionDataArgs['programAddress'];\n};\n\nexport function getCreateAccountWithSeedInstruction<\n  TAccountPayer extends string,\n  TAccountNewAccount extends string,\n  TAccountBaseAccount extends string,\n  TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS,\n>(\n  input: CreateAccountWithSeedInput<\n    TAccountPayer,\n    TAccountNewAccount,\n    TAccountBaseAccount\n  >,\n  config?: { programAddress?: TProgramAddress }\n): CreateAccountWithSeedInstruction<\n  TProgramAddress,\n  TAccountPayer,\n  TAccountNewAccount,\n  TAccountBaseAccount\n> {\n  // Program address.\n  const programAddress = config?.programAddress ?? SYSTEM_PROGRAM_ADDRESS;\n\n  // Original accounts.\n  const originalAccounts = {\n    payer: { value: input.payer ?? null, isWritable: true },\n    newAccount: { value: input.newAccount ?? null, isWritable: true },\n    baseAccount: { value: input.baseAccount ?? null, isWritable: false },\n  };\n  const accounts = originalAccounts as Record<\n    keyof typeof originalAccounts,\n    ResolvedAccount\n  >;\n\n  // Original args.\n  const args = { ...input };\n\n  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');\n  const instruction = {\n    accounts: [\n      getAccountMeta(accounts.payer),\n      getAccountMeta(accounts.newAccount),\n      getAccountMeta(accounts.baseAccount),\n    ],\n    programAddress,\n    data: getCreateAccountWithSeedInstructionDataEncoder().encode(\n      args as CreateAccountWithSeedInstructionDataArgs\n    ),\n  } as CreateAccountWithSeedInstruction<\n    TProgramAddress,\n    TAccountPayer,\n    TAccountNewAccount,\n    TAccountBaseAccount\n  >;\n\n  return instruction;\n}\n\nexport type ParsedCreateAccountWithSeedInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],\n> = {\n  programAddress: Address<TProgram>;\n  accounts: {\n    payer: TAccountMetas[0];\n    newAccount: TAccountMetas[1];\n    baseAccount: TAccountMetas[2];\n  };\n  data: CreateAccountWithSeedInstructionData;\n};\n\nexport function parseCreateAccountWithSeedInstruction<\n  TProgram extends string,\n  TAccountMetas extends readonly IAccountMeta[],\n>(\n  instruction: IInstruction<TProgram> &\n    IInstructionWithAccounts<TAccountMetas> &\n    IInstructionWithData<Uint8Array>\n): ParsedCreateAccountWithSeedInstruction<TProgram, TAccountMetas> {\n  if (instruction.accounts.length < 3) {\n    // TODO: Coded error.\n    throw new Error('Not enough accounts');\n  }\n  let accountIndex = 0;\n  const getNextAccount = () => {\n    const accountMeta = instruction.accounts![accountIndex]!;\n    accountIndex += 1;\n    return accountMeta;\n  };\n  return {\n    programAddress: instruction.programAddress,\n    accounts: {\n      payer: getNextAccount(),\n      newAccount: getNextAccount(),\n      baseAccount: getNextAccount(),\n    },\n    data: getCreateAccountWithSeedInstructionDataDecoder().decode(\n      instruction.data\n    ),\n  };\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  combineCodec,\n  getAddressDecoder,\n  getAddressEncoder,\n  getStructDecoder,\n  getStructEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n  type ReadonlyAccount,\n  type WritableAccount,\n} from '@solana/kit';\nimport { SYSTEM_PROGRAM_ADDRESS } from '../programs';\nimport { getAccountMetaFactory, type ResolvedAccount } from '../shared';\n\nexport const INITIALIZE_NONCE_ACCOUNT_DISCRIMINATOR = 6;\n\nexport function getInitializeNonceAccountDiscriminatorBytes() {\n  return getU32Encoder().encode(INITIALIZE_NONCE_ACCOUNT_DISCRIMINATOR);\n}\n\nexport type InitializeNonceAccountInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountNonceAccount extends string | IAccountMeta<string> = string,\n  TAccountRecentBlockhashesSysvar extends\n    | string\n    | IAccountMeta<string> = 'SysvarRecentB1ockHashes11111111111111111111',\n  TAccountRentSysvar extends\n    | string\n    | IAccountMeta<string> = 'SysvarRent111111111111111111111111111111111',\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<\n    [\n      TAccountNonceAccount extends string\n        ? WritableAccount<TAccountNonceAccount>\n        : TAccountNonceAccount,\n      TAccountRecentBlockhashesSysvar extends string\n        ? ReadonlyAccount<TAccountRecentBlockhashesSysvar>\n        : TAccountRecentBlockhashesSysvar,\n      TAccountRentSysvar extends string\n        ? ReadonlyAccount<TAccountRentSysvar>\n        : TAccountRentSysvar,\n      ...TRemainingAccounts,\n    ]\n  >;\n\nexport type InitializeNonceAccountInstructionData = {\n  discriminator: number;\n  nonceAuthority: Address;\n};\n\nexport type InitializeNonceAccountInstructionDataArgs = {\n  nonceAuthority: Address;\n};\n\nexport function getInitializeNonceAccountInstructionDataEncoder(): Encoder<InitializeNonceAccountInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([\n      ['discriminator', getU32Encoder()],\n      ['nonceAuthority', getAddressEncoder()],\n    ]),\n    (value) => ({\n      ...value,\n      discriminator: INITIALIZE_NONCE_ACCOUNT_DISCRIMINATOR,\n    })\n  );\n}\n\nexport function getInitializeNonceAccountInstructionDataDecoder(): Decoder<InitializeNonceAccountInstructionData> {\n  return getStructDecoder([\n    ['discriminator', getU32Decoder()],\n    ['nonceAuthority', getAddressDecoder()],\n  ]);\n}\n\nexport function getInitializeNonceAccountInstructionDataCodec(): Codec<\n  InitializeNonceAccountInstructionDataArgs,\n  InitializeNonceAccountInstructionData\n> {\n  return combineCodec(\n    getInitializeNonceAccountInstructionDataEncoder(),\n    getInitializeNonceAccountInstructionDataDecoder()\n  );\n}\n\nexport type InitializeNonceAccountInput<\n  TAccountNonceAccount extends string = string,\n  TAccountRecentBlockhashesSysvar extends string = string,\n  TAccountRentSysvar extends string = string,\n> = {\n  nonceAccount: Address<TAccountNonceAccount>;\n  recentBlockhashesSysvar?: Address<TAccountRecentBlockhashesSysvar>;\n  rentSysvar?: Address<TAccountRentSysvar>;\n  nonceAuthority: InitializeNonceAccountInstructionDataArgs['nonceAuthority'];\n};\n\nexport function getInitializeNonceAccountInstruction<\n  TAccountNonceAccount extends string,\n  TAccountRecentBlockhashesSysvar extends string,\n  TAccountRentSysvar extends string,\n  TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS,\n>(\n  input: InitializeNonceAccountInput<\n    TAccountNonceAccount,\n    TAccountRecentBlockhashesSysvar,\n    TAccountRentSysvar\n  >,\n  config?: { programAddress?: TProgramAddress }\n): InitializeNonceAccountInstruction<\n  TProgramAddress,\n  TAccountNonceAccount,\n  TAccountRecentBlockhashesSysvar,\n  TAccountRentSysvar\n> {\n  // Program address.\n  const programAddress = config?.programAddress ?? SYSTEM_PROGRAM_ADDRESS;\n\n  // Original accounts.\n  const originalAccounts = {\n    nonceAccount: { value: input.nonceAccount ?? null, isWritable: true },\n    recentBlockhashesSysvar: {\n      value: input.recentBlockhashesSysvar ?? null,\n      isWritable: false,\n    },\n    rentSysvar: { value: input.rentSysvar ?? null, isWritable: false },\n  };\n  const accounts = originalAccounts as Record<\n    keyof typeof originalAccounts,\n    ResolvedAccount\n  >;\n\n  // Original args.\n  const args = { ...input };\n\n  // Resolve default values.\n  if (!accounts.recentBlockhashesSysvar.value) {\n    accounts.recentBlockhashesSysvar.value =\n      'SysvarRecentB1ockHashes11111111111111111111' as Address<'SysvarRecentB1ockHashes11111111111111111111'>;\n  }\n  if (!accounts.rentSysvar.value) {\n    accounts.rentSysvar.value =\n      'SysvarRent111111111111111111111111111111111' as Address<'SysvarRent111111111111111111111111111111111'>;\n  }\n\n  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');\n  const instruction = {\n    accounts: [\n      getAccountMeta(accounts.nonceAccount),\n      getAccountMeta(accounts.recentBlockhashesSysvar),\n      getAccountMeta(accounts.rentSysvar),\n    ],\n    programAddress,\n    data: getInitializeNonceAccountInstructionDataEncoder().encode(\n      args as InitializeNonceAccountInstructionDataArgs\n    ),\n  } as InitializeNonceAccountInstruction<\n    TProgramAddress,\n    TAccountNonceAccount,\n    TAccountRecentBlockhashesSysvar,\n    TAccountRentSysvar\n  >;\n\n  return instruction;\n}\n\nexport type ParsedInitializeNonceAccountInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],\n> = {\n  programAddress: Address<TProgram>;\n  accounts: {\n    nonceAccount: TAccountMetas[0];\n    recentBlockhashesSysvar: TAccountMetas[1];\n    rentSysvar: TAccountMetas[2];\n  };\n  data: InitializeNonceAccountInstructionData;\n};\n\nexport function parseInitializeNonceAccountInstruction<\n  TProgram extends string,\n  TAccountMetas extends readonly IAccountMeta[],\n>(\n  instruction: IInstruction<TProgram> &\n    IInstructionWithAccounts<TAccountMetas> &\n    IInstructionWithData<Uint8Array>\n): ParsedInitializeNonceAccountInstruction<TProgram, TAccountMetas> {\n  if (instruction.accounts.length < 3) {\n    // TODO: Coded error.\n    throw new Error('Not enough accounts');\n  }\n  let accountIndex = 0;\n  const getNextAccount = () => {\n    const accountMeta = instruction.accounts![accountIndex]!;\n    accountIndex += 1;\n    return accountMeta;\n  };\n  return {\n    programAddress: instruction.programAddress,\n    accounts: {\n      nonceAccount: getNextAccount(),\n      recentBlockhashesSysvar: getNextAccount(),\n      rentSysvar: getNextAccount(),\n    },\n    data: getInitializeNonceAccountInstructionDataDecoder().decode(\n      instruction.data\n    ),\n  };\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  combineCodec,\n  getStructDecoder,\n  getStructEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  getU64Decoder,\n  getU64Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IAccountSignerMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n  type TransactionSigner,\n  type WritableAccount,\n  type WritableSignerAccount,\n} from '@solana/kit';\nimport { SYSTEM_PROGRAM_ADDRESS } from '../programs';\nimport { getAccountMetaFactory, type ResolvedAccount } from '../shared';\n\nexport const TRANSFER_SOL_DISCRIMINATOR = 2;\n\nexport function getTransferSolDiscriminatorBytes() {\n  return getU32Encoder().encode(TRANSFER_SOL_DISCRIMINATOR);\n}\n\nexport type TransferSolInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountSource extends string | IAccountMeta<string> = string,\n  TAccountDestination extends string | IAccountMeta<string> = string,\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<\n    [\n      TAccountSource extends string\n        ? WritableSignerAccount<TAccountSource> &\n            IAccountSignerMeta<TAccountSource>\n        : TAccountSource,\n      TAccountDestination extends string\n        ? WritableAccount<TAccountDestination>\n        : TAccountDestination,\n      ...TRemainingAccounts,\n    ]\n  >;\n\nexport type TransferSolInstructionData = {\n  discriminator: number;\n  amount: bigint;\n};\n\nexport type TransferSolInstructionDataArgs = { amount: number | bigint };\n\nexport function getTransferSolInstructionDataEncoder(): Encoder<TransferSolInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([\n      ['discriminator', getU32Encoder()],\n      ['amount', getU64Encoder()],\n    ]),\n    (value) => ({ ...value, discriminator: TRANSFER_SOL_DISCRIMINATOR })\n  );\n}\n\nexport function getTransferSolInstructionDataDecoder(): Decoder<TransferSolInstructionData> {\n  return getStructDecoder([\n    ['discriminator', getU32Decoder()],\n    ['amount', getU64Decoder()],\n  ]);\n}\n\nexport function getTransferSolInstructionDataCodec(): Codec<\n  TransferSolInstructionDataArgs,\n  TransferSolInstructionData\n> {\n  return combineCodec(\n    getTransferSolInstructionDataEncoder(),\n    getTransferSolInstructionDataDecoder()\n  );\n}\n\nexport type TransferSolInput<\n  TAccountSource extends string = string,\n  TAccountDestination extends string = string,\n> = {\n  source: TransactionSigner<TAccountSource>;\n  destination: Address<TAccountDestination>;\n  amount: TransferSolInstructionDataArgs['amount'];\n};\n\nexport function getTransferSolInstruction<\n  TAccountSource extends string,\n  TAccountDestination extends string,\n  TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS,\n>(\n  input: TransferSolInput<TAccountSource, TAccountDestination>,\n  config?: { programAddress?: TProgramAddress }\n): TransferSolInstruction<\n  TProgramAddress,\n  TAccountSource,\n  TAccountDestination\n> {\n  // Program address.\n  const programAddress = config?.programAddress ?? SYSTEM_PROGRAM_ADDRESS;\n\n  // Original accounts.\n  const originalAccounts = {\n    source: { value: input.source ?? null, isWritable: true },\n    destination: { value: input.destination ?? null, isWritable: true },\n  };\n  const accounts = originalAccounts as Record<\n    keyof typeof originalAccounts,\n    ResolvedAccount\n  >;\n\n  // Original args.\n  const args = { ...input };\n\n  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');\n  const instruction = {\n    accounts: [\n      getAccountMeta(accounts.source),\n      getAccountMeta(accounts.destination),\n    ],\n    programAddress,\n    data: getTransferSolInstructionDataEncoder().encode(\n      args as TransferSolInstructionDataArgs\n    ),\n  } as TransferSolInstruction<\n    TProgramAddress,\n    TAccountSource,\n    TAccountDestination\n  >;\n\n  return instruction;\n}\n\nexport type ParsedTransferSolInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],\n> = {\n  programAddress: Address<TProgram>;\n  accounts: {\n    source: TAccountMetas[0];\n    destination: TAccountMetas[1];\n  };\n  data: TransferSolInstructionData;\n};\n\nexport function parseTransferSolInstruction<\n  TProgram extends string,\n  TAccountMetas extends readonly IAccountMeta[],\n>(\n  instruction: IInstruction<TProgram> &\n    IInstructionWithAccounts<TAccountMetas> &\n    IInstructionWithData<Uint8Array>\n): ParsedTransferSolInstruction<TProgram, TAccountMetas> {\n  if (instruction.accounts.length < 2) {\n    // TODO: Coded error.\n    throw new Error('Not enough accounts');\n  }\n  let accountIndex = 0;\n  const getNextAccount = () => {\n    const accountMeta = instruction.accounts![accountIndex]!;\n    accountIndex += 1;\n    return accountMeta;\n  };\n  return {\n    programAddress: instruction.programAddress,\n    accounts: {\n      source: getNextAccount(),\n      destination: getNextAccount(),\n    },\n    data: getTransferSolInstructionDataDecoder().decode(instruction.data),\n  };\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  addDecoderSizePrefix,\n  addEncoderSizePrefix,\n  combineCodec,\n  getAddressDecoder,\n  getAddressEncoder,\n  getStructDecoder,\n  getStructEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  getU64Decoder,\n  getU64Encoder,\n  getUtf8Decoder,\n  getUtf8Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IAccountSignerMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n  type ReadonlySignerAccount,\n  type TransactionSigner,\n  type WritableAccount,\n} from '@solana/kit';\nimport { SYSTEM_PROGRAM_ADDRESS } from '../programs';\nimport { getAccountMetaFactory, type ResolvedAccount } from '../shared';\n\nexport const TRANSFER_SOL_WITH_SEED_DISCRIMINATOR = 11;\n\nexport function getTransferSolWithSeedDiscriminatorBytes() {\n  return getU32Encoder().encode(TRANSFER_SOL_WITH_SEED_DISCRIMINATOR);\n}\n\nexport type TransferSolWithSeedInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountSource extends string | IAccountMeta<string> = string,\n  TAccountBaseAccount extends string | IAccountMeta<string> = string,\n  TAccountDestination extends string | IAccountMeta<string> = string,\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<\n    [\n      TAccountSource extends string\n        ? WritableAccount<TAccountSource>\n        : TAccountSource,\n      TAccountBaseAccount extends string\n        ? ReadonlySignerAccount<TAccountBaseAccount> &\n            IAccountSignerMeta<TAccountBaseAccount>\n        : TAccountBaseAccount,\n      TAccountDestination extends string\n        ? WritableAccount<TAccountDestination>\n        : TAccountDestination,\n      ...TRemainingAccounts,\n    ]\n  >;\n\nexport type TransferSolWithSeedInstructionData = {\n  discriminator: number;\n  amount: bigint;\n  fromSeed: string;\n  fromOwner: Address;\n};\n\nexport type TransferSolWithSeedInstructionDataArgs = {\n  amount: number | bigint;\n  fromSeed: string;\n  fromOwner: Address;\n};\n\nexport function getTransferSolWithSeedInstructionDataEncoder(): Encoder<TransferSolWithSeedInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([\n      ['discriminator', getU32Encoder()],\n      ['amount', getU64Encoder()],\n      ['fromSeed', addEncoderSizePrefix(getUtf8Encoder(), getU64Encoder())],\n      ['fromOwner', getAddressEncoder()],\n    ]),\n    (value) => ({\n      ...value,\n      discriminator: TRANSFER_SOL_WITH_SEED_DISCRIMINATOR,\n    })\n  );\n}\n\nexport function getTransferSolWithSeedInstructionDataDecoder(): Decoder<TransferSolWithSeedInstructionData> {\n  return getStructDecoder([\n    ['discriminator', getU32Decoder()],\n    ['amount', getU64Decoder()],\n    ['fromSeed', addDecoderSizePrefix(getUtf8Decoder(), getU64Decoder())],\n    ['fromOwner', getAddressDecoder()],\n  ]);\n}\n\nexport function getTransferSolWithSeedInstructionDataCodec(): Codec<\n  TransferSolWithSeedInstructionDataArgs,\n  TransferSolWithSeedInstructionData\n> {\n  return combineCodec(\n    getTransferSolWithSeedInstructionDataEncoder(),\n    getTransferSolWithSeedInstructionDataDecoder()\n  );\n}\n\nexport type TransferSolWithSeedInput<\n  TAccountSource extends string = string,\n  TAccountBaseAccount extends string = string,\n  TAccountDestination extends string = string,\n> = {\n  source: Address<TAccountSource>;\n  baseAccount: TransactionSigner<TAccountBaseAccount>;\n  destination: Address<TAccountDestination>;\n  amount: TransferSolWithSeedInstructionDataArgs['amount'];\n  fromSeed: TransferSolWithSeedInstructionDataArgs['fromSeed'];\n  fromOwner: TransferSolWithSeedInstructionDataArgs['fromOwner'];\n};\n\nexport function getTransferSolWithSeedInstruction<\n  TAccountSource extends string,\n  TAccountBaseAccount extends string,\n  TAccountDestination extends string,\n  TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS,\n>(\n  input: TransferSolWithSeedInput<\n    TAccountSource,\n    TAccountBaseAccount,\n    TAccountDestination\n  >,\n  config?: { programAddress?: TProgramAddress }\n): TransferSolWithSeedInstruction<\n  TProgramAddress,\n  TAccountSource,\n  TAccountBaseAccount,\n  TAccountDestination\n> {\n  // Program address.\n  const programAddress = config?.programAddress ?? SYSTEM_PROGRAM_ADDRESS;\n\n  // Original accounts.\n  const originalAccounts = {\n    source: { value: input.source ?? null, isWritable: true },\n    baseAccount: { value: input.baseAccount ?? null, isWritable: false },\n    destination: { value: input.destination ?? null, isWritable: true },\n  };\n  const accounts = originalAccounts as Record<\n    keyof typeof originalAccounts,\n    ResolvedAccount\n  >;\n\n  // Original args.\n  const args = { ...input };\n\n  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');\n  const instruction = {\n    accounts: [\n      getAccountMeta(accounts.source),\n      getAccountMeta(accounts.baseAccount),\n      getAccountMeta(accounts.destination),\n    ],\n    programAddress,\n    data: getTransferSolWithSeedInstructionDataEncoder().encode(\n      args as TransferSolWithSeedInstructionDataArgs\n    ),\n  } as TransferSolWithSeedInstruction<\n    TProgramAddress,\n    TAccountSource,\n    TAccountBaseAccount,\n    TAccountDestination\n  >;\n\n  return instruction;\n}\n\nexport type ParsedTransferSolWithSeedInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],\n> = {\n  programAddress: Address<TProgram>;\n  accounts: {\n    source: TAccountMetas[0];\n    baseAccount: TAccountMetas[1];\n    destination: TAccountMetas[2];\n  };\n  data: TransferSolWithSeedInstructionData;\n};\n\nexport function parseTransferSolWithSeedInstruction<\n  TProgram extends string,\n  TAccountMetas extends readonly IAccountMeta[],\n>(\n  instruction: IInstruction<TProgram> &\n    IInstructionWithAccounts<TAccountMetas> &\n    IInstructionWithData<Uint8Array>\n): ParsedTransferSolWithSeedInstruction<TProgram, TAccountMetas> {\n  if (instruction.accounts.length < 3) {\n    // TODO: Coded error.\n    throw new Error('Not enough accounts');\n  }\n  let accountIndex = 0;\n  const getNextAccount = () => {\n    const accountMeta = instruction.accounts![accountIndex]!;\n    accountIndex += 1;\n    return accountMeta;\n  };\n  return {\n    programAddress: instruction.programAddress,\n    accounts: {\n      source: getNextAccount(),\n      baseAccount: getNextAccount(),\n      destination: getNextAccount(),\n    },\n    data: getTransferSolWithSeedInstructionDataDecoder().decode(\n      instruction.data\n    ),\n  };\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  combineCodec,\n  getStructDecoder,\n  getStructEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n  type WritableAccount,\n} from '@solana/kit';\nimport { SYSTEM_PROGRAM_ADDRESS } from '../programs';\nimport { getAccountMetaFactory, type ResolvedAccount } from '../shared';\n\nexport const UPGRADE_NONCE_ACCOUNT_DISCRIMINATOR = 12;\n\nexport function getUpgradeNonceAccountDiscriminatorBytes() {\n  return getU32Encoder().encode(UPGRADE_NONCE_ACCOUNT_DISCRIMINATOR);\n}\n\nexport type UpgradeNonceAccountInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountNonceAccount extends string | IAccountMeta<string> = string,\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<\n    [\n      TAccountNonceAccount extends string\n        ? WritableAccount<TAccountNonceAccount>\n        : TAccountNonceAccount,\n      ...TRemainingAccounts,\n    ]\n  >;\n\nexport type UpgradeNonceAccountInstructionData = { discriminator: number };\n\nexport type UpgradeNonceAccountInstructionDataArgs = {};\n\nexport function getUpgradeNonceAccountInstructionDataEncoder(): Encoder<UpgradeNonceAccountInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([['discriminator', getU32Encoder()]]),\n    (value) => ({\n      ...value,\n      discriminator: UPGRADE_NONCE_ACCOUNT_DISCRIMINATOR,\n    })\n  );\n}\n\nexport function getUpgradeNonceAccountInstructionDataDecoder(): Decoder<UpgradeNonceAccountInstructionData> {\n  return getStructDecoder([['discriminator', getU32Decoder()]]);\n}\n\nexport function getUpgradeNonceAccountInstructionDataCodec(): Codec<\n  UpgradeNonceAccountInstructionDataArgs,\n  UpgradeNonceAccountInstructionData\n> {\n  return combineCodec(\n    getUpgradeNonceAccountInstructionDataEncoder(),\n    getUpgradeNonceAccountInstructionDataDecoder()\n  );\n}\n\nexport type UpgradeNonceAccountInput<\n  TAccountNonceAccount extends string = string,\n> = {\n  nonceAccount: Address<TAccountNonceAccount>;\n};\n\nexport function getUpgradeNonceAccountInstruction<\n  TAccountNonceAccount extends string,\n  TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS,\n>(\n  input: UpgradeNonceAccountInput<TAccountNonceAccount>,\n  config?: { programAddress?: TProgramAddress }\n): UpgradeNonceAccountInstruction<TProgramAddress, TAccountNonceAccount> {\n  // Program address.\n  const programAddress = config?.programAddress ?? SYSTEM_PROGRAM_ADDRESS;\n\n  // Original accounts.\n  const originalAccounts = {\n    nonceAccount: { value: input.nonceAccount ?? null, isWritable: true },\n  };\n  const accounts = originalAccounts as Record<\n    keyof typeof originalAccounts,\n    ResolvedAccount\n  >;\n\n  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');\n  const instruction = {\n    accounts: [getAccountMeta(accounts.nonceAccount)],\n    programAddress,\n    data: getUpgradeNonceAccountInstructionDataEncoder().encode({}),\n  } as UpgradeNonceAccountInstruction<TProgramAddress, TAccountNonceAccount>;\n\n  return instruction;\n}\n\nexport type ParsedUpgradeNonceAccountInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],\n> = {\n  programAddress: Address<TProgram>;\n  accounts: {\n    nonceAccount: TAccountMetas[0];\n  };\n  data: UpgradeNonceAccountInstructionData;\n};\n\nexport function parseUpgradeNonceAccountInstruction<\n  TProgram extends string,\n  TAccountMetas extends readonly IAccountMeta[],\n>(\n  instruction: IInstruction<TProgram> &\n    IInstructionWithAccounts<TAccountMetas> &\n    IInstructionWithData<Uint8Array>\n): ParsedUpgradeNonceAccountInstruction<TProgram, TAccountMetas> {\n  if (instruction.accounts.length < 1) {\n    // TODO: Coded error.\n    throw new Error('Not enough accounts');\n  }\n  let accountIndex = 0;\n  const getNextAccount = () => {\n    const accountMeta = instruction.accounts![accountIndex]!;\n    accountIndex += 1;\n    return accountMeta;\n  };\n  return {\n    programAddress: instruction.programAddress,\n    accounts: {\n      nonceAccount: getNextAccount(),\n    },\n    data: getUpgradeNonceAccountInstructionDataDecoder().decode(\n      instruction.data\n    ),\n  };\n}\n", "/**\n * This code was AUTOGENERATED using the codama library.\n * Please DO NOT EDIT THIS FILE, instead use visitors\n * to add features, then rerun codama to update it.\n *\n * @see https://github.com/codama-idl/codama\n */\n\nimport {\n  combineCodec,\n  getStructDecoder,\n  getStructEncoder,\n  getU32Decoder,\n  getU32Encoder,\n  getU64Decoder,\n  getU64Encoder,\n  transformEncoder,\n  type Address,\n  type Codec,\n  type Decoder,\n  type Encoder,\n  type IAccountMeta,\n  type IAccountSignerMeta,\n  type IInstruction,\n  type IInstructionWithAccounts,\n  type IInstructionWithData,\n  type ReadonlyAccount,\n  type ReadonlySignerAccount,\n  type TransactionSigner,\n  type WritableAccount,\n} from '@solana/kit';\nimport { SYSTEM_PROGRAM_ADDRESS } from '../programs';\nimport { getAccountMetaFactory, type ResolvedAccount } from '../shared';\n\nexport const WITHDRAW_NONCE_ACCOUNT_DISCRIMINATOR = 5;\n\nexport function getWithdrawNonceAccountDiscriminatorBytes() {\n  return getU32Encoder().encode(WITHDRAW_NONCE_ACCOUNT_DISCRIMINATOR);\n}\n\nexport type WithdrawNonceAccountInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountNonceAccount extends string | IAccountMeta<string> = string,\n  TAccountRecipientAccount extends string | IAccountMeta<string> = string,\n  TAccountRecentBlockhashesSysvar extends\n    | string\n    | IAccountMeta<string> = 'SysvarRecentB1ockHashes11111111111111111111',\n  TAccountRentSysvar extends\n    | string\n    | IAccountMeta<string> = 'SysvarRent111111111111111111111111111111111',\n  TAccountNonceAuthority extends string | IAccountMeta<string> = string,\n  TRemainingAccounts extends readonly IAccountMeta<string>[] = [],\n> = IInstruction<TProgram> &\n  IInstructionWithData<Uint8Array> &\n  IInstructionWithAccounts<\n    [\n      TAccountNonceAccount extends string\n        ? WritableAccount<TAccountNonceAccount>\n        : TAccountNonceAccount,\n      TAccountRecipientAccount extends string\n        ? WritableAccount<TAccountRecipientAccount>\n        : TAccountRecipientAccount,\n      TAccountRecentBlockhashesSysvar extends string\n        ? ReadonlyAccount<TAccountRecentBlockhashesSysvar>\n        : TAccountRecentBlockhashesSysvar,\n      TAccountRentSysvar extends string\n        ? ReadonlyAccount<TAccountRentSysvar>\n        : TAccountRentSysvar,\n      TAccountNonceAuthority extends string\n        ? ReadonlySignerAccount<TAccountNonceAuthority> &\n            IAccountSignerMeta<TAccountNonceAuthority>\n        : TAccountNonceAuthority,\n      ...TRemainingAccounts,\n    ]\n  >;\n\nexport type WithdrawNonceAccountInstructionData = {\n  discriminator: number;\n  withdrawAmount: bigint;\n};\n\nexport type WithdrawNonceAccountInstructionDataArgs = {\n  withdrawAmount: number | bigint;\n};\n\nexport function getWithdrawNonceAccountInstructionDataEncoder(): Encoder<WithdrawNonceAccountInstructionDataArgs> {\n  return transformEncoder(\n    getStructEncoder([\n      ['discriminator', getU32Encoder()],\n      ['withdrawAmount', getU64Encoder()],\n    ]),\n    (value) => ({\n      ...value,\n      discriminator: WITHDRAW_NONCE_ACCOUNT_DISCRIMINATOR,\n    })\n  );\n}\n\nexport function getWithdrawNonceAccountInstructionDataDecoder(): Decoder<WithdrawNonceAccountInstructionData> {\n  return getStructDecoder([\n    ['discriminator', getU32Decoder()],\n    ['withdrawAmount', getU64Decoder()],\n  ]);\n}\n\nexport function getWithdrawNonceAccountInstructionDataCodec(): Codec<\n  WithdrawNonceAccountInstructionDataArgs,\n  WithdrawNonceAccountInstructionData\n> {\n  return combineCodec(\n    getWithdrawNonceAccountInstructionDataEncoder(),\n    getWithdrawNonceAccountInstructionDataDecoder()\n  );\n}\n\nexport type WithdrawNonceAccountInput<\n  TAccountNonceAccount extends string = string,\n  TAccountRecipientAccount extends string = string,\n  TAccountRecentBlockhashesSysvar extends string = string,\n  TAccountRentSysvar extends string = string,\n  TAccountNonceAuthority extends string = string,\n> = {\n  nonceAccount: Address<TAccountNonceAccount>;\n  recipientAccount: Address<TAccountRecipientAccount>;\n  recentBlockhashesSysvar?: Address<TAccountRecentBlockhashesSysvar>;\n  rentSysvar?: Address<TAccountRentSysvar>;\n  nonceAuthority: TransactionSigner<TAccountNonceAuthority>;\n  withdrawAmount: WithdrawNonceAccountInstructionDataArgs['withdrawAmount'];\n};\n\nexport function getWithdrawNonceAccountInstruction<\n  TAccountNonceAccount extends string,\n  TAccountRecipientAccount extends string,\n  TAccountRecentBlockhashesSysvar extends string,\n  TAccountRentSysvar extends string,\n  TAccountNonceAuthority extends string,\n  TProgramAddress extends Address = typeof SYSTEM_PROGRAM_ADDRESS,\n>(\n  input: WithdrawNonceAccountInput<\n    TAccountNonceAccount,\n    TAccountRecipientAccount,\n    TAccountRecentBlockhashesSysvar,\n    TAccountRentSysvar,\n    TAccountNonceAuthority\n  >,\n  config?: { programAddress?: TProgramAddress }\n): WithdrawNonceAccountInstruction<\n  TProgramAddress,\n  TAccountNonceAccount,\n  TAccountRecipientAccount,\n  TAccountRecentBlockhashesSysvar,\n  TAccountRentSysvar,\n  TAccountNonceAuthority\n> {\n  // Program address.\n  const programAddress = config?.programAddress ?? SYSTEM_PROGRAM_ADDRESS;\n\n  // Original accounts.\n  const originalAccounts = {\n    nonceAccount: { value: input.nonceAccount ?? null, isWritable: true },\n    recipientAccount: {\n      value: input.recipientAccount ?? null,\n      isWritable: true,\n    },\n    recentBlockhashesSysvar: {\n      value: input.recentBlockhashesSysvar ?? null,\n      isWritable: false,\n    },\n    rentSysvar: { value: input.rentSysvar ?? null, isWritable: false },\n    nonceAuthority: { value: input.nonceAuthority ?? null, isWritable: false },\n  };\n  const accounts = originalAccounts as Record<\n    keyof typeof originalAccounts,\n    ResolvedAccount\n  >;\n\n  // Original args.\n  const args = { ...input };\n\n  // Resolve default values.\n  if (!accounts.recentBlockhashesSysvar.value) {\n    accounts.recentBlockhashesSysvar.value =\n      'SysvarRecentB1ockHashes11111111111111111111' as Address<'SysvarRecentB1ockHashes11111111111111111111'>;\n  }\n  if (!accounts.rentSysvar.value) {\n    accounts.rentSysvar.value =\n      'SysvarRent111111111111111111111111111111111' as Address<'SysvarRent111111111111111111111111111111111'>;\n  }\n\n  const getAccountMeta = getAccountMetaFactory(programAddress, 'programId');\n  const instruction = {\n    accounts: [\n      getAccountMeta(accounts.nonceAccount),\n      getAccountMeta(accounts.recipientAccount),\n      getAccountMeta(accounts.recentBlockhashesSysvar),\n      getAccountMeta(accounts.rentSysvar),\n      getAccountMeta(accounts.nonceAuthority),\n    ],\n    programAddress,\n    data: getWithdrawNonceAccountInstructionDataEncoder().encode(\n      args as WithdrawNonceAccountInstructionDataArgs\n    ),\n  } as WithdrawNonceAccountInstruction<\n    TProgramAddress,\n    TAccountNonceAccount,\n    TAccountRecipientAccount,\n    TAccountRecentBlockhashesSysvar,\n    TAccountRentSysvar,\n    TAccountNonceAuthority\n  >;\n\n  return instruction;\n}\n\nexport type ParsedWithdrawNonceAccountInstruction<\n  TProgram extends string = typeof SYSTEM_PROGRAM_ADDRESS,\n  TAccountMetas extends readonly IAccountMeta[] = readonly IAccountMeta[],\n> = {\n  programAddress: Address<TProgram>;\n  accounts: {\n    nonceAccount: TAccountMetas[0];\n    recipientAccount: TAccountMetas[1];\n    recentBlockhashesSysvar: TAccountMetas[2];\n    rentSysvar: TAccountMetas[3];\n    nonceAuthority: TAccountMetas[4];\n  };\n  data: WithdrawNonceAccountInstructionData;\n};\n\nexport function parseWithdrawNonceAccountInstruction<\n  TProgram extends string,\n  TAccountMetas extends readonly IAccountMeta[],\n>(\n  instruction: IInstruction<TProgram> &\n    IInstructionWithAccounts<TAccountMetas> &\n    IInstructionWithData<Uint8Array>\n): ParsedWithdrawNonceAccountInstruction<TProgram, TAccountMetas> {\n  if (instruction.accounts.length < 5) {\n    // TODO: Coded error.\n    throw new Error('Not enough accounts');\n  }\n  let accountIndex = 0;\n  const getNextAccount = () => {\n    const accountMeta = instruction.accounts![accountIndex]!;\n    accountIndex += 1;\n    return accountMeta;\n  };\n  return {\n    programAddress: instruction.programAddress,\n    accounts: {\n      nonceAccount: getNextAccount(),\n      recipientAccount: getNextAccount(),\n      recentBlockhashesSysvar: getNextAccount(),\n      rentSysvar: getNextAccount(),\n      nonceAuthority: getNextAccount(),\n    },\n    data: getWithdrawNonceAccountInstructionDataDecoder().decode(\n      instruction.data\n    ),\n  };\n}\n"]}