import { notNullish } from '@kdt310722/utils/common'
import { tap } from '@kdt310722/utils/function'
import { createSolanaRpcFromTransport, createSolanaRpcSubscriptions } from '@solana/kit'
import { config } from '../config'
import { createSolanaRpcTransport } from '../utils/rpc-client/transports'
import { toTransportResponse } from '../utils/rpc-client/utils'
import { createChildLogger } from './logger'

const logger = createChildLogger('clients:rpc')

const createTransport = (params: typeof config.rpcClient.http) => createSolanaRpcTransport({
    ...params,
    onRequest: (request) => tap(logger.createTimer(`rpc-client:request:${request.id}`), () => logger.debug('Rpc request', request)),
    onResponse: (body, response, request) => logger.stopTimer(`rpc-client:request:${request.id}`, 'debug', 'Rpc response', { request: request.id, response: toTransportResponse(body, response) }),
    retry: { ...params.retry, onFailedAttempt: (error) => logger.warn('Request failed, retrying...', error) },
})

export const rpcSubscriptions = createSolanaRpcSubscriptions(config.rpcClient.ws)
export const rpcHttpClient = createSolanaRpcFromTransport(createTransport(config.rpcClient.http))
export const rpcSendClient = notNullish(config.rpcClient.send) ? createSolanaRpcFromTransport(createTransport(config.rpcClient.send)) : rpcHttpClient

async function checkHttpRpcConnection() {
    const stop = logger.createLoading().start('Checking HTTP RPC client connection...')

    await rpcHttpClient.getHealth().send().then(() => {
        stop(`HTTP RPC client connection is healthy!`)
    })
}

export async function initializeRpcClient() {
    await checkHttpRpcConnection()
}
