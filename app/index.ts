import { initializeBuy } from './common/buy'
import { initializeDexScreener } from './common/dexscreener'
import { initializeRaydium } from './common/raydium'
import { initializeTracker } from './common/tracker'
import { initializeWallet } from './common/wallet'
import { initializeDatabase } from './core/database'
import { initializeRpcClient } from './core/rpc-client'
import 'reflect-metadata'

await initializeDatabase()
await initializeRpcClient()
await initializeWallet()
await initializeRaydium()
await initializeTracker()
await initializeDexScreener()
await initializeBuy()
